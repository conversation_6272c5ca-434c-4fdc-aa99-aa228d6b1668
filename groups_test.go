package mnms

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
)

func TestGroupValidation(t *testing.T) {
	// Test valid relm
	validRelm := &Relm{
		Name:     "test-relm",
		Comment:  "Test relm",
		Location: "Test location",
	}
	err := ValidateRelm(validRelm)
	assert.NoError(t, err)

	// Test invalid relm name (empty)
	invalidRelm := &Relm{
		Name: "",
	}
	err = ValidateRelm(invalidRelm)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "relm name cannot be empty")

	// Test invalid relm name (special characters)
	invalidRelm.Name = "test@relm"
	err = ValidateRelm(invalidRelm)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must contain only alphanumeric characters")

	// Test duplicate region names
	duplicateRegionRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{Name: "region1"},
			{Name: "region1"}, // duplicate
		},
	}
	err = ValidateRelm(duplicateRegionRelm)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "duplicate region name")
}

func TestMacAddressValidation(t *testing.T) {
	// Valid MAC addresses
	validMacs := []string{
		"00-60-E9-2D-91-3E",
		"AA-BB-CC-DD-EE-FF",
		"12-34-56-78-9A-BC",
	}

	for _, mac := range validMacs {
		assert.True(t, isValidMacAddress(mac), "MAC %s should be valid", mac)
	}

	// Invalid MAC addresses
	invalidMacs := []string{
		"00:60:E9:2D:91:3E",    // wrong separator
		"00-60-E9-2D-91",       // too short
		"00-60-E9-2D-91-3E-FF", // too long
		"GG-60-E9-2D-91-3E",    // invalid hex
		"",                     // empty
	}

	for _, mac := range invalidMacs {
		assert.False(t, isValidMacAddress(mac), "MAC %s should be invalid", mac)
	}
}

func TestNameValidation(t *testing.T) {
	// Valid names
	validNames := []string{
		"test",
		"test-name",
		"test_name",
		"Test123",
		"a",
		"test-123_ABC",
	}

	for _, name := range validNames {
		assert.True(t, isValidName(name), "Name %s should be valid", name)
	}

	// Invalid names
	invalidNames := []string{
		"",           // empty
		"test@name",  // special character
		"test name",  // space
		"test.name",  // dot
		"test/name",  // slash
		"test name!", // exclamation
	}

	for _, name := range invalidNames {
		assert.False(t, isValidName(name), "Name %s should be invalid", name)
	}

	// Test name length limit
	longName := ""
	for i := 0; i < 65; i++ {
		longName += "a"
	}
	assert.False(t, isValidName(longName), "Name longer than 64 characters should be invalid")
}

func TestGroupManagerOperations(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Test creating a relm
	testRelm := &Relm{
		Name:     "test-relm",
		Comment:  "Test relm for unit testing",
		Location: "Test location",
		Regions:  []Region{},
	}

	// Mock the persistence by directly adding to QC.GroupData instead of using CreateRelm
	QC.GroupMutex.Lock()
	QC.GroupData[testRelm.Name] = testRelm
	QC.GroupMutex.Unlock()

	// Test getting the relm
	retrievedRelm, err := gm.GetRelm("test-relm")
	assert.NoError(t, err)
	assert.Equal(t, testRelm.Name, retrievedRelm.Name)
	assert.Equal(t, testRelm.Comment, retrievedRelm.Comment)

	// Test creating duplicate relm (manually check)
	QC.GroupMutex.Lock()
	_, exists := QC.GroupData[testRelm.Name]
	QC.GroupMutex.Unlock()
	assert.True(t, exists, "Relm should already exist")

	// Test adding a region
	testRegion := Region{
		Name:    "test-region",
		Comment: "Test region",
		Zones:   []Zone{},
	}

	// Manually add region to avoid persistence
	QC.GroupMutex.Lock()
	QC.GroupData["test-relm"].Regions = append(QC.GroupData["test-relm"].Regions, testRegion)
	QC.GroupMutex.Unlock()

	// Verify region was added
	updatedRelm, err := gm.GetRelm("test-relm")
	assert.NoError(t, err)
	assert.Len(t, updatedRelm.Regions, 1)
	assert.Equal(t, "test-region", updatedRelm.Regions[0].Name)

	// Test adding a zone
	testZone := Zone{
		Name:    "test-zone",
		Comment: "Test zone",
		Subnets: []Subnet{},
	}

	// Manually add zone to avoid persistence
	QC.GroupMutex.Lock()
	QC.GroupData["test-relm"].Regions[0].Zones = append(QC.GroupData["test-relm"].Regions[0].Zones, testZone)
	QC.GroupMutex.Unlock()

	// Test adding a subnet
	testSubnet := Subnet{
		Name:         "test-subnet",
		Comment:      "Test subnet",
		SubnetGroups: []SubnetGroup{},
	}

	// Manually add subnet to avoid persistence
	QC.GroupMutex.Lock()
	QC.GroupData["test-relm"].Regions[0].Zones[0].Subnets = append(QC.GroupData["test-relm"].Regions[0].Zones[0].Subnets, testSubnet)
	QC.GroupMutex.Unlock()

	// Test adding a device (should now return error directing to use SubnetGroup)
	testDeviceMac := "00-60-E9-2D-91-3E"
	err = gm.AddDeviceToSubnet("test-relm", "test-region", "test-zone", "test-subnet", testDeviceMac)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "use AddDeviceToSubnetGroup instead")

	// Test removing a device (should now return error directing to use SubnetGroup)
	err = gm.RemoveDeviceFromSubnet("test-relm", "test-region", "test-zone", "test-subnet", testDeviceMac)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "use RemoveDeviceFromSubnetGroup instead")

	// Test deleting the relm (manually without persistence)
	QC.GroupMutex.Lock()
	delete(QC.GroupData, "test-relm")
	QC.GroupMutex.Unlock()

	// Verify relm was deleted
	_, err = gm.GetRelm("test-relm")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestGroupManagerErrorCases(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Test operations on non-existent relm
	_, err := gm.GetRelm("non-existent")
	assert.Error(t, err)

	// Test adding region to non-existent relm
	testRegion := Region{Name: "test-region"}
	err = gm.AddRegionToRelm("non-existent", testRegion)
	assert.Error(t, err)

	// Create a relm for further testing (manually without persistence)
	testRelm := &Relm{
		Name:    "test-relm",
		Regions: []Region{},
	}
	QC.GroupMutex.Lock()
	QC.GroupData[testRelm.Name] = testRelm
	QC.GroupMutex.Unlock()

	// Test adding zone to non-existent region
	testZone := Zone{Name: "test-zone"}
	err = gm.AddZoneToRegion("test-relm", "non-existent-region", testZone)
	assert.Error(t, err)

	// Test adding duplicate region (manually without persistence)
	QC.GroupMutex.Lock()
	QC.GroupData["test-relm"].Regions = append(QC.GroupData["test-relm"].Regions, testRegion)
	QC.GroupMutex.Unlock()

	// Try to add the same region again
	err = gm.AddRegionToRelm("test-relm", testRegion)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already exists")
}

func TestFindDeviceInGroups(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Create test structure
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name: "test-subnet",
								SubnetGroups: []SubnetGroup{
									{
										Name:    "test-subnetgroup",
										Devices: []string{"00-60-E9-2D-91-3E", "00-60-E9-2D-91-3F"},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// Manually add relm to avoid persistence
	QC.GroupMutex.Lock()
	QC.GroupData[testRelm.Name] = testRelm
	QC.GroupMutex.Unlock()

	// Test finding existing device (now returns SubnetGroup name as well)
	relmName, regionName, zoneName, subnetName, subnetGroupName, err := gm.FindDeviceInGroups("00-60-E9-2D-91-3E")
	assert.NoError(t, err)
	assert.Equal(t, "test-relm", relmName)
	assert.Equal(t, "test-region", regionName)
	assert.Equal(t, "test-zone", zoneName)
	assert.Equal(t, "test-subnet", subnetName)
	assert.Equal(t, "test-subnetgroup", subnetGroupName)

	// Test finding non-existent device
	_, _, _, _, _, err = gm.FindDeviceInGroups("00-60-E9-2D-91-99")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestGetDevicesInSubnet(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Create test structure
	testDevices := []string{"00-60-E9-2D-91-3E", "00-60-E9-2D-91-3F"}
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name: "test-subnet",
								SubnetGroups: []SubnetGroup{
									{
										Name:    "test-subnetgroup",
										Devices: testDevices,
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// Manually add relm to avoid persistence
	QC.GroupMutex.Lock()
	QC.GroupData[testRelm.Name] = testRelm
	QC.GroupMutex.Unlock()

	// Test getting devices from existing subnet
	devices, err := gm.GetDevicesInSubnet("test-relm", "test-region", "test-zone", "test-subnet")
	assert.NoError(t, err)
	assert.Equal(t, testDevices, devices)

	// Test getting devices from non-existent subnet
	_, err = gm.GetDevicesInSubnet("test-relm", "test-region", "test-zone", "non-existent")
	assert.Error(t, err)
}

func TestGroupHTTPHandlers(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)

	// Create a test router with simplified handlers that don't use persistence
	r := chi.NewRouter()
	r.Route("/api/v1/groups", func(r chi.Router) {
		r.Get("/", func(w http.ResponseWriter, r *http.Request) {
			QC.GroupMutex.Lock()
			defer QC.GroupMutex.Unlock()
			jsonBytes, _ := json.Marshal(QC.GroupData)
			w.Write(jsonBytes)
		})
		r.Post("/", func(w http.ResponseWriter, r *http.Request) {
			body, _ := io.ReadAll(r.Body)
			var relm Relm
			json.Unmarshal(body, &relm)
			QC.GroupMutex.Lock()
			QC.GroupData[relm.Name] = &relm
			QC.GroupMutex.Unlock()
			jsonBytes, _ := json.Marshal(relm)
			w.Write(jsonBytes)
		})
		r.Get("/{relmName}", func(w http.ResponseWriter, r *http.Request) {
			relmName := chi.URLParam(r, "relmName")
			QC.GroupMutex.Lock()
			relm, exists := QC.GroupData[relmName]
			QC.GroupMutex.Unlock()
			if !exists {
				w.WriteHeader(http.StatusNotFound)
				return
			}
			jsonBytes, _ := json.Marshal(relm)
			w.Write(jsonBytes)
		})
		r.Delete("/{relmName}", func(w http.ResponseWriter, r *http.Request) {
			relmName := chi.URLParam(r, "relmName")
			QC.GroupMutex.Lock()
			delete(QC.GroupData, relmName)
			QC.GroupMutex.Unlock()
			w.Write([]byte(`{"message": "relm deleted successfully"}`))
		})
	})

	// Test creating a group
	testRelm := Relm{
		Name:     "test-relm",
		Comment:  "Test relm",
		Location: "Test location",
	}

	jsonData, _ := json.Marshal(testRelm)
	req := httptest.NewRequest("POST", "/api/v1/groups", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test getting all groups
	req = httptest.NewRequest("GET", "/api/v1/groups", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var groups map[string]*Relm
	err := json.Unmarshal(w.Body.Bytes(), &groups)
	assert.NoError(t, err)
	assert.Len(t, groups, 1)

	// Test getting a specific group
	req = httptest.NewRequest("GET", "/api/v1/groups/test-relm", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var retrievedRelm Relm
	err = json.Unmarshal(w.Body.Bytes(), &retrievedRelm)
	assert.NoError(t, err)
	assert.Equal(t, "test-relm", retrievedRelm.Name)

	// Test deleting the group
	req = httptest.NewRequest("DELETE", "/api/v1/groups/test-relm", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGroupHTTPErrorCases(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)

	// Create a test router
	r := chi.NewRouter()
	r.Route("/api/v1/groups", func(r chi.Router) {
		r.Get("/{relmName}", HandleGetGroup)
		r.Post("/", HandleCreateGroup)
		r.Delete("/{relmName}", HandleDeleteGroup)
	})

	// Test getting non-existent group
	req := httptest.NewRequest("GET", "/api/v1/groups/non-existent", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Test creating group with invalid data
	invalidData := `{"name": ""}`
	req = httptest.NewRequest("POST", "/api/v1/groups", bytes.NewBufferString(invalidData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Test creating group with invalid JSON
	req = httptest.NewRequest("POST", "/api/v1/groups", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Test deleting non-existent group
	req = httptest.NewRequest("DELETE", "/api/v1/groups/non-existent", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestGetDeviceSubnetName(t *testing.T) {
	// Initialize test data
	QC.GroupData = make(map[string]*Relm)

	// Create test group structure with SubnetGroups
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name: "test-subnet",
								SubnetGroups: []SubnetGroup{
									{
										Name:    "test-subnetgroup",
										Devices: []string{"AA-BB-CC-DD-EE-FF", "11-22-33-44-55-66"},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	QC.GroupData["test-relm"] = testRelm

	// Test GetDeviceSubnetName (now returns SubnetGroup name)
	gm := &GroupManager{}

	// Device in subnet group should return subnet group name
	subnetGroupName := gm.GetDeviceSubnetName("AA-BB-CC-DD-EE-FF")
	assert.Equal(t, "test-subnetgroup", subnetGroupName)

	// Device in subnet group should return subnet group name
	subnetGroupName = gm.GetDeviceSubnetName("11-22-33-44-55-66")
	assert.Equal(t, "test-subnetgroup", subnetGroupName)

	// Device not in any subnet group should return empty string
	subnetGroupName = gm.GetDeviceSubnetName("99-88-77-66-55-44")
	assert.Equal(t, "", subnetGroupName)
}

func TestDeleteOperationsLogic(t *testing.T) {
	// Initialize test data
	QC.GroupData = make(map[string]*Relm)

	// Create test group structure
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name: "test-subnet",
								SubnetGroups: []SubnetGroup{
									{
										Name:    "test-subnetgroup",
										Devices: []string{"AA-BB-CC-DD-EE-FF"},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	QC.GroupData["test-relm"] = testRelm

	// Test that we can find the structures before deletion
	relm := QC.GroupData["test-relm"]
	assert.Equal(t, 1, len(relm.Regions))
	assert.Equal(t, 1, len(relm.Regions[0].Zones))
	assert.Equal(t, 1, len(relm.Regions[0].Zones[0].Subnets))
	assert.Equal(t, "test-subnet", relm.Regions[0].Zones[0].Subnets[0].Name)

	// Test manual deletion logic (without persistence)
	// Simulate DeleteSubnet logic
	regionIdx := 0
	zoneIdx := 0
	subnetIdx := 0

	// Remove the subnet from the slice
	relm.Regions[regionIdx].Zones[zoneIdx].Subnets = append(
		relm.Regions[regionIdx].Zones[zoneIdx].Subnets[:subnetIdx],
		relm.Regions[regionIdx].Zones[zoneIdx].Subnets[subnetIdx+1:]...,
	)

	// Verify subnet is deleted
	assert.Equal(t, 0, len(relm.Regions[0].Zones[0].Subnets))

	// Test error cases for non-existent items
	// This should return error for non-existent relm (without trying to persist)
	QC.GroupData = make(map[string]*Relm) // Clear data
	_, exists := QC.GroupData["non-existent"]
	assert.False(t, exists)
}

func TestSubnetGroupOperations(t *testing.T) {
	// Initialize test data
	QC.GroupData = make(map[string]*Relm)

	// Create test group structure
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name:         "test-subnet",
								SubnetGroups: []SubnetGroup{},
							},
						},
					},
				},
			},
		},
	}

	QC.GroupData["test-relm"] = testRelm

	gm := &GroupManager{}

	// Test AddSubnetGroup logic without persistence
	subnetGroupData := SubnetGroup{
		Name:    "test-subnetgroup",
		Comment: "Test subnet group",
		Devices: []string{},
	}

	// Manually add subnet group to test data structure (without persistence)
	QC.GroupMutex.Lock()
	relm := QC.GroupData["test-relm"]
	relm.Regions[0].Zones[0].Subnets[0].SubnetGroups = append(
		relm.Regions[0].Zones[0].Subnets[0].SubnetGroups,
		subnetGroupData,
	)
	QC.GroupMutex.Unlock()

	// Verify subnet group was added
	relm = QC.GroupData["test-relm"]
	assert.Equal(t, 1, len(relm.Regions[0].Zones[0].Subnets[0].SubnetGroups))
	assert.Equal(t, "test-subnetgroup", relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Name)

	// Test device operations without persistence (manual operations)
	// Manually add device to subnet group
	QC.GroupMutex.Lock()
	relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices = append(
		relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices,
		"AA-BB-CC-DD-EE-FF",
	)
	QC.GroupMutex.Unlock()

	// Verify device was added
	assert.Equal(t, 1, len(relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices))
	assert.Equal(t, "AA-BB-CC-DD-EE-FF", relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices[0])

	// Test GetDeviceSubnetName function
	subnetGroupName := gm.GetDeviceSubnetName("AA-BB-CC-DD-EE-FF")
	assert.Equal(t, "test-subnetgroup", subnetGroupName)

	// Test GetAllAssignedDevices function
	assignedDevices := gm.GetAllAssignedDevices()
	assert.True(t, assignedDevices["AA-BB-CC-DD-EE-FF"])

	// Manually remove device from subnet group
	QC.GroupMutex.Lock()
	relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices = []string{}
	QC.GroupMutex.Unlock()

	// Verify device was removed
	assert.Equal(t, 0, len(relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices))

	// Manually delete subnet group
	QC.GroupMutex.Lock()
	relm.Regions[0].Zones[0].Subnets[0].SubnetGroups = []SubnetGroup{}
	QC.GroupMutex.Unlock()

	// Verify subnet group was deleted
	assert.Equal(t, 0, len(relm.Regions[0].Zones[0].Subnets[0].SubnetGroups))
}

func TestDeviceSubnetGroupOneToOneMapping(t *testing.T) {
	// Initialize test data
	QC.GroupData = make(map[string]*Relm)

	// Create test group structure with two subnet groups
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name: "test-subnet",
								SubnetGroups: []SubnetGroup{
									{
										Name:    "test-subnetgroup-1",
										Devices: []string{},
									},
									{
										Name:    "test-subnetgroup-2",
										Devices: []string{},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	QC.GroupData["test-relm"] = testRelm

	gm := &GroupManager{}

	// Manually add device to first subnet group (without persistence)
	QC.GroupMutex.Lock()
	relm := QC.GroupData["test-relm"]
	relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices = append(
		relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices,
		"AA-BB-CC-DD-EE-FF",
	)
	QC.GroupMutex.Unlock()

	// Verify device was added to first subnet group
	assert.Equal(t, 1, len(relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices))
	assert.Equal(t, "AA-BB-CC-DD-EE-FF", relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices[0])

	// Test 1-to-1 mapping constraint: try to add same device to second subnet group
	// This should fail because device is already assigned to first subnet group
	deviceMac := "AA-BB-CC-DD-EE-FF"
	currentSubnetGroupName := gm.GetDeviceSubnetName(deviceMac)
	assert.Equal(t, "test-subnetgroup-1", currentSubnetGroupName)

	// Simulate the constraint check that happens in AddDeviceToSubnetGroup
	if currentSubnetGroupName != "" {
		// This is the expected behavior - device is already assigned
		assert.Equal(t, "test-subnetgroup-1", currentSubnetGroupName)
	}

	// Test GetAllAssignedDevices function
	assignedDevices := gm.GetAllAssignedDevices()
	assert.True(t, assignedDevices["AA-BB-CC-DD-EE-FF"])
	assert.False(t, assignedDevices["BB-CC-DD-EE-FF-00"]) // Non-existent device

	// Manually remove device from first subnet group
	QC.GroupMutex.Lock()
	relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices = []string{}
	QC.GroupMutex.Unlock()

	// Verify device was removed
	assert.Equal(t, 0, len(relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[0].Devices))

	// Now device should be available for assignment (GetDeviceSubnetName should return empty)
	currentSubnetGroupName = gm.GetDeviceSubnetName(deviceMac)
	assert.Equal(t, "", currentSubnetGroupName)

	// Manually add device to second subnet group
	QC.GroupMutex.Lock()
	relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[1].Devices = append(
		relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[1].Devices,
		"AA-BB-CC-DD-EE-FF",
	)
	QC.GroupMutex.Unlock()

	// Verify device was added to second subnet group
	assert.Equal(t, 1, len(relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[1].Devices))
	assert.Equal(t, "AA-BB-CC-DD-EE-FF", relm.Regions[0].Zones[0].Subnets[0].SubnetGroups[1].Devices[0])
}
