package mnms

import (
	"bytes"
	"compress/gzip"
	"embed"
	"encoding/json"
	"fmt"
	"io"
	"io/fs"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/go-chi/jwtauth/v5"
	"github.com/pquerna/otp/totp"
	"github.com/qeof/q"
)

//go:embed dist
var staticFS embed.FS

// simplest and stupid http api

var httpRunning bool

// PostWithToken encapsulates the http POST request with the token
func PostWithToken(url, token string, body io.Reader) (resp *http.Response, err error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err = client.Do(req)
	if err != nil {
		q.Q(err.Error())
		return nil, err
	}
	return resp, nil
}

// GetWithToken encapsulates the http GET request with the token
func GetWithToken(url, token string) (resp *http.Response, err error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err = client.Do(req)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	return resp, nil
}

// EnsureStaticFilesFolderExist ensures static files folder exist
func EnsureStaticFilesFolderExist() (string, error) {
	// check static files folder
	nmswd, err := CheckMNMSFolder()
	if err != nil {
		q.Q(err)
		nmswd = "."
	}
	fileDir := path.Join(nmswd, "files")
	// check if folder exist
	if _, err := os.Stat(fileDir); os.IsNotExist(err) {
		err := os.Mkdir(fileDir, 0o755)
		if err != nil {
			q.Q(err)
			return "", err
		}
	}
	return fileDir, nil
}

// EnsureDeviceImageFolderExist ensures device image folder exist
func EnsureDeviceImageFolderExist() (string, error) {
	// check static files folder
	nmswd, err := CheckMNMSFolder()
	if err != nil {
		q.Q(err)
		nmswd = "."
	}
	fileDir := path.Join(nmswd, "device-images")
	// check if folder exist
	if _, err := os.Stat(fileDir); os.IsNotExist(err) {
		err := os.Mkdir(fileDir, 0o755)
		if err != nil {
			q.Q(err)
			return "", err
		}
	}
	return fileDir, nil
}

// FileServer conveniently sets up a http.FileServer handler to serve
// static files from a http.FileSystem.
func FileServer(r chi.Router, path string, root http.FileSystem) {
	if len(path) == 0 {
		q.Q("FileServer cannot be used with an empty path")

		return
	}

	if strings.ContainsAny(path, "{}*") {
		q.Q("FileServer does not permit any URL parameters.")
		return
	}

	if path != "/" && path[len(path)-1] != '/' {
		r.Get(path, http.RedirectHandler(path+"/", http.StatusMovedPermanently).ServeHTTP)
		path += "/"
	}
	path += "*"
	r.Get(path, func(w http.ResponseWriter, r *http.Request) {
		ctx := chi.RouteContext(r.Context())
		pathPrefix := strings.TrimSuffix(ctx.RoutePattern(), "/*")
		fs := http.StripPrefix(pathPrefix, http.FileServer(root))
		fs.ServeHTTP(w, r)
	})
}

// NetworkAgentRouter

// BuildRouter builds http router
func BuildRouter() http.Handler {
	r := chi.NewRouter()
	r.Use(middleware.Timeout(60 * time.Second))
	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
	}))

	r.Use(middleware.NewCompressor(gzip.DefaultCompression).Handler)
	// r.Use(middleware.SetHeader("Content-Type", "application/json"))
	r.HandleFunc("/api", func(w http.ResponseWriter, r *http.Request) {
		_, err := w.Write([]byte("mnms says hello"))
		if err != nil {
			q.Q(err)
		}
	})

	varifySuperUser := func(next http.Handler) http.Handler {
		return JWTAuthenticatorRole(MNMSSuperUserRole, next)
	}
	varifyAdmin := func(next http.Handler) http.Handler {
		return JWTAuthenticatorRole(MNMSAdminRole, next)
	}
	// static file directory
	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		q.Q(err)
	}
	deviceImageDir, err := EnsureDeviceImageFolderExist()
	if err != nil {
		q.Q(err)
	}

	r.Route("/api/v1", func(r chi.Router) {
		r.Use(middleware.SetHeader("Content-Type", "application/json"))
		r.HandleFunc("/login", HandleLogin)
		r.Post("/2fa/validate", HandleValidate2FA)
		r.HandleFunc("/ws", WsEndpoint)
		r.Post("/ws/post", WsPostEndpoint)
		r.HandleFunc("/register", HandleRegister)
		FileServer(r, "/device-images", http.Dir(deviceImageDir))

		r.Post("/validateconfig", HandleValidateConfig)
		// admin permission
		r.Group(func(r chi.Router) {
			r.Use(jwtauth.Verifier(jwtTokenAuth))
			r.Use(varifyAdmin)
			r.Post("/users", HandleAddUser)
			r.Put("/users", HandleUpdateUser)
			r.Delete("/users", HandleDeleteUser)
		})

		// superuser permission
		r.Group(func(r chi.Router) {
			r.Use(jwtauth.Verifier(jwtTokenAuth))
			r.Use(varifySuperUser)

			// KVstore
			r.Post("/kvstore", HandlePostKVStore)
			r.Post("/kvstore/import", HandlePostKVStore)

			// ai-assistant
			r.Post("/commands", HandleCommands)
			r.Post("/devices", HandleDevices)
			r.Post("/topology", HandleTopology)
			r.Post("/topology/action", handleTopologyAction)
			r.Get("/network-topology", HandleNetworkTopology)
			r.Get("/subnet-groups", HandleGetSubnetGroups)
			r.Get("/syslogs", HandleLocalSyslogs)
			r.Get("/syslogs/alert", HandleAlertSyslogs)

			r.Post("/logs", HandleLogs)
			r.Post("/wg", HandleWireguard)
			r.Post("/tcpproxy", HandleTcpProxy)

			r.Post("/service/update", HandleServiceUpdate)

			r.Post("/idps/report", HandleIdpsReport)
			r.Get("/idps/report", HandleIdpsReport)
			r.Post("/clients", HandleClientInfo)
			r.Post("/ssh/tunnels", HandleSshTunnels)
			r.Post("/forwards", HandleForwards)
		})
		// user permission
		r.Group(func(r chi.Router) {
			r.Use(jwtauth.Verifier(jwtTokenAuth))
			r.Use(jwtauth.Authenticator)
			// KVstore
			r.Get("/kvstore/:{key}", HandleGetKVValue)
			r.Get("/kvstore", HandleKVStoreBatch)
			r.Get("/kvstore/export", HandleExportKVStore)

			r.With(JWTRenewAgentToken).Get("/commands", HandleCommands)
			r.Get("/devices", HandleDevices)
			r.Get("/topology", HandleTopology)
			r.Get("/network-topology", HandleNetworkTopology)
			r.Get("/logs", HandleLogs)
			r.Get("/users", HandleUsers)
			r.HandleFunc("/2fa/secret", Handle2FA)
			r.Get("/info", HandleInfo)
			r.Get("/license-info", HandleLicenseInfo)
			r.Get("/wg", HandleWireguard)
			r.Get("/tcpproxy", HandleTcpProxy)

			r.HandleFunc("/agent/version", HandleAgentVersion)
			r.HandleFunc("/agent/openvpn/keys", HandleAgentOpenvpnKeys)
			r.HandleFunc("/agent/firmware", HandleAgentFirmware)
			r.HandleFunc("/agent/ports", HandleAgentPort)

			FileServer(r, "/files", http.Dir(fileDir))

			r.Get("/service/update", HandleServiceUpdate)

			r.Post("/idps/report", HandleIdpsReport)
			r.Get("/idps/report", HandleIdpsReport)
			r.Get("/clients", HandleClientInfo)
			r.Get("/ssh/tunnels", HandleSshTunnels)
			r.Get("/machines/services", HandleMachinesServices)
			r.Get("/forwards", HandleForwards)

			// Group management
			r.Route("/groups", func(r chi.Router) {
				r.Get("/", HandleListGroups)
				r.Post("/", HandleCreateGroup)
				r.Post("/cleanup", HandleCleanupGroups)
				r.Get("/{relmName}", HandleGetGroup)
				r.Put("/{relmName}", HandleUpdateGroup)
				r.Delete("/{relmName}", HandleDeleteGroup)

				// Hierarchical operations
				r.Post("/{relmName}/regions", HandleAddRegion)
				r.Delete("/{relmName}/regions/{regionName}", HandleDeleteRegion)
				r.Post("/{relmName}/regions/{regionName}/zones", HandleAddZone)
				r.Delete("/{relmName}/regions/{regionName}/zones/{zoneName}", HandleDeleteZone)
				r.Post("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets", HandleAddSubnet)
				r.Delete("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}", HandleDeleteSubnet)
				r.Post("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups", HandleAddSubnetGroup)
				r.Delete("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups/{subnetGroupName}", HandleDeleteSubnetGroup)
				r.Post("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups/{subnetGroupName}/devices", HandleAddDeviceToSubnetGroup)
				r.Delete("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups/{subnetGroupName}/devices/{deviceMac}", HandleRemoveDeviceFromSubnetGroup)
				r.Post("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/devices", HandleAddDeviceToSubnet)
				r.Delete("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/devices/{deviceMac}", HandleRemoveDeviceFromSubnet)

				// Device management helpers
				r.Get("/devices/unassigned", HandleGetUnassignedDevices)
				r.Get("/devices/transfer", HandleGetDevicesForTransfer)
				r.Get("/devices/available/{subnetName}", HandleGetAvailableDevicesForSubnet)
				r.Get("/statistics", HandleGetGroupStatistics)
			})
		})
	})
	if QC.IsRoot {
		statiscFileSystem := newStaticFileSystem()
		fs := http.FileServer(statiscFileSystem.FileSystem)
		r.Get("/*", func(w http.ResponseWriter, r *http.Request) {
			rctx := chi.RouteContext(r.Context())
			pathPrefix := strings.TrimSuffix(rctx.RoutePattern(), "/*")
			if !statiscFileSystem.exists("", r.RequestURI) {
				pathPrefix = strings.TrimSuffix(r.RequestURI, "/*")
			}
			fs := http.StripPrefix(pathPrefix, fs)
			fs.ServeHTTP(w, r)
		})
	}
	return r
}

// HTTPMain starts http api service, skipTLS = true if you dont want to serve https
func HTTPMain() {
	var wg sync.WaitGroup
	if httpRunning { // hack for the tests
		return
	}
	httpRunning = true

	// Start a go routine
	wg.Add(1)
	go func() {
		defer wg.Done()
		WebSocketStartWriteMessage()
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		r := BuildRouter()
		httpAddr := fmt.Sprintf(":%d", QC.Port)
		q.Q(httpAddr)

		err := http.ListenAndServe(httpAddr, r)
		if err != nil {
			q.Q("error: cannot run http server", httpAddr, err)
			fmt.Println("error: cannot run http server", httpAddr, err)
			DoExit(1)
		}

		// server.ListenAndServeTLS("", "")
	}()

	wg.Wait()
}

// RespondWithError write error to the response
func RespondWithError(w http.ResponseWriter, err error) {
	q.Q(err)
	// response code = internal server error
	w.WriteHeader(http.StatusInternalServerError)
	errorInfo := make(map[string]string)
	errorInfo["error"] = fmt.Sprintf("%v", err)
	jsonBytes, err := json.Marshal(errorInfo)
	if err != nil {
		q.Q(err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// ResponseOK write ok to the response
func ResponseOK(w http.ResponseWriter) {
	w.WriteHeader(http.StatusOK)
	resp := map[string]string{"status": "ok"}
	jsonBytes, err := json.Marshal(resp)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleCommands accepts the commands and returns command info and history

POST /api/v1/commands

	   Example parameter: (map[string]CmdInfo)
	{ "beep 01-22-33-44-55-66" : {}, "devices publish", : {"all": true} }

GET /api/v1/commands?id=client1

	retrieve commands intended for service client1

GET /api/v1/commands?cmd=beep 01-22-33-44-55-66

	retrieve command status of a particular command

Aditional query parameters:
GET /api/v1/commands?last=5

	Retrieve last 5 commands in the command history. Note that the return map key is sequence number, not the command.

GET /api/v1/commands?staus=ok

	Retrieve all commands with status ok.

GET /api/v1/commands?staus=error

	Retrieve all commands with status error: as prefix.

GET /api/v1/commands?staus=ok&last=5

	Retrieve last 5 commands with status ok. Note that the return map key is swquence number, not the command.

Get /api/v1/commands?pendingTimeOver=30

	retrieve all commands with status empty and pending over 30 seconds

GET /api/v1/commands?pendingTimeOver=30&last=5

	retrieve last 5 commands with status empty and pending over 30 seconds. Note that the return map key is sequence number, not the command.
*/
func HandleCommands(w http.ResponseWriter, r *http.Request) {
	// enableCors(&w)
	q.Q("handle cmds", r.Method)
	if r.Method == "POST" {
		bodyText, err := io.ReadAll(r.Body)
		if err != nil {
			q.Q("error: can't read resp body", err)
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()
		cmdList := []CmdInfo{}

		err = json.Unmarshal(bodyText, &cmdList)
		if err != nil {
			q.Q("error: can't unmarshal cmd list", err)
			RespondWithError(w, err)
			return
		}
		// Process each command in cmdList with normalize the spaces commands
		for i := range cmdList {
			cmdList[i].Command = NormalizeCmdsSpaces(cmdList[i].Command)
		}
		q.Q("handling cmd list", cmdList)
		validatedCmdList := []CmdInfo{}
		invalidatedCmdList := []CmdInfo{}
		for _, v := range cmdList {
			k := v.Command
			if v.Status == "ok" || strings.HasPrefix(v.Status, "error: ") {
				if v.Client == QC.Name {
					// only executed command from agent will be logged otherwise it will generate too much log especially for nested nms
					sendCmdSyslog("ReceivedRunCmd", &v)
				}
			}
			QC.CmdMutex.Lock()
			_, ok := QC.CmdData[k]
			QC.CmdMutex.Unlock()
			if v.Edit == "delete" {
				q.Q("edit, deleting cmd", k, v)
				if !ok {
					q.Q("cmd not found, deleting cmd", k)
					RespondWithError(w, fmt.Errorf("command %s not found", k))
				}
				QC.CmdMutex.Lock()
				delete(QC.CmdData, k)
				QC.CmdMutex.Unlock()
				continue
			}
			cmdinfo, err := ValidateCommands(&v)
			if err != nil {
				invalidatedCmdList = append(invalidatedCmdList, *cmdinfo)
			} else {
				validatedCmdList = append(validatedCmdList, *cmdinfo)
			}
		}
		UpdateInvalidCmds(invalidatedCmdList)
		UpdateCmds(validatedCmdList)

		cmdResp := append(validatedCmdList, invalidatedCmdList...)
		jsonBytes, err := json.Marshal(cmdResp)
		if err != nil {
			RespondWithError(w, err)
			return
		}

		// forward request to root
		if !QC.IsRoot && QC.RootURL != "" {
			q.Q("forward request to root", cmdResp)
			// forward request to root
			resp, err := PostWithToken(QC.RootURL+"/api/v1/commands", QC.AdminToken, bytes.NewBuffer(jsonBytes))
			if err != nil {
				RespondWithError(w, err)
				return
			}
			defer resp.Body.Close()

			_, err = io.Copy(w, resp.Body)
			if err != nil {
				q.Q(err)
			}
			return
		}

		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	// id := r.URL.Query().Get("id")
	ids := r.URL.Query()["id"]
	cmd := r.URL.Query().Get("cmd")
	kind := r.URL.Query().Get("kind")
	prefix := r.URL.Query().Get("prefix")
	devid := r.URL.Query().Get("devid")
	size := r.URL.Query().Get("size")
	last := r.URL.Query().Get("last")
	status := r.URL.Query().Get("status")
	pendingTimeOver := r.URL.Query().Get("pendingTimeOver")

	// GET
	cmddata := make(map[string]CmdInfo)
	cmd = NormalizeCmdsSpaces(cmd)
	q.Q(ids, kind, cmd)
	if cmd != "" {
		q.Q("get cmd info", cmd)
		// clients wants info on a specific cmd
		if cmd == "all" {
			q.Q("client wants to get all commands")
			QC.CmdMutex.Lock()
			cmddata = QC.CmdData
			QC.CmdMutex.Unlock()
		} else {
			q.Q("clients wants", cmd)
			QC.CmdMutex.Lock()
			for k, v := range QC.CmdData {
				if v.Command == cmd {
					cmddata[k] = v
				}
				q.Q("found", k)
			}
			QC.CmdMutex.Unlock()
		}
		jsonBytes, err := json.Marshal(cmddata)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	if len(status) > 0 || len(pendingTimeOver) > 0 || len(last) > 0 {
		q.Q("retrieve ", status, pendingTimeOver, last)
		QC.CmdMutex.Lock()
		for k, v := range QC.CmdData {
			if len(status) == 0 && len(pendingTimeOver) == 0 {
				break
			}
			if len(status) > 0 {
				if status == "error" {
					if strings.HasPrefix(v.Status, "error: ") {
						cmddata[k] = v
					}
				} else {
					if v.Status == status {
						cmddata[k] = v
					}
				}
			} else if len(pendingTimeOver) > 0 {
				// status == "" && now - cmd.timestamp > pending
				if v.Status == "" {
					t, err := strconv.Atoi(pendingTimeOver)
					if err != nil {
						RespondWithError(w, err)
						return
					}
					// timestamp is unix string time format
					timestamp, err := strconv.Atoi(v.Timestamp)
					if err != nil {
						RespondWithError(w, err)
						return
					}
					if int(time.Now().Unix())-timestamp > t {
						cmddata[k] = v
					}
				}
			}
		}

		if len(last) > 0 {
			lastCount, err := strconv.Atoi(last)
			if err != nil {
				RespondWithError(w, err)
				return
			}
			if len(cmddata) == 0 {
				cmddata = QC.CmdData
			}
			cmddata, err = RetrieveLastCmds(LastCmdsQuery{Count: lastCount}, cmddata)
			if err != nil {
				RespondWithError(w, err)
				return
			}
		}
		QC.CmdMutex.Unlock()
		jsonBytes, err := json.Marshal(cmddata)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	if prefix == "agent" {
		size_int := 0
		index := 0
		if v, err := strconv.Atoi(size); err == nil {
			// number
			size_int = v
			if v <= 0 {
				size_int = 0
			}
		}

		cmddata := make(map[string]CmdInfo)
		q.Q("agent wants to get agent commands")
		QC.CmdMutex.Lock()
		for k, v := range QC.CmdData {
			if v.Status == "" &&
				strings.Contains(v.DevId, devid) &&
				strings.HasPrefix(v.Command, "agent") {
				if size_int == 0 {
					cmddata[k] = v
				} else if index < size_int {
					index++
					cmddata[k] = v
				} else {
					break
				}
			}
		}
		QC.CmdMutex.Unlock()
		jsonBytes, err := json.Marshal(cmddata)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	q.Q("get all non status cmds")
	nonNmsServices := []string{"syslog", "idps", "forward"}
	QC.CmdMutex.Lock()
	for k, v := range QC.CmdData {
		// Add commands to cmddata which status is empty
		if v.Status == "" {
			// ignore if request has specific cli
			if v.Client != "" && !slices.Contains(ids, v.Client) {
				continue
			}

			// if v.Name == QC.Name {
			// 	continue
			// }

			ws := strings.Fields(v.Command)
			if len(ws) < 2 {
				continue
			}

			// if kind is specified, only return commands of that kind
			if slices.Contains(nonNmsServices, kind) {
				if !slices.Contains(ids, v.Client) {
					continue
				}
				if ws[0] != kind && slices.Contains(nonNmsServices, ws[0]) {
					continue
				}
				cmddata[k] = v
				continue
			}

			// if kind is nms, only return commands that are not of the nonNmsServices
			if kind == "nms" {
				if slices.Contains(nonNmsServices, ws[0]) {
					continue
				}
				cmddata[k] = v
				continue
			}

			// if kind is not specified, return all commands
			cmddata[k] = v
		}
	}
	QC.CmdMutex.Unlock()
	q.Q("sending to client", cmddata)
	jsonBytes, err := json.Marshal(cmddata)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

func HandleSnmpPagination(foundData CmdInfo, pageSize, pageNum, totalPage string) CmdInfo {
	if strings.HasPrefix(foundData.Status, "error: ") {
		return foundData
	}
	type walkResult struct {
		Oid   string
		Value string
	}

	walksResultData := []walkResult{}
	resultData := foundData.Result
	err := json.Unmarshal([]byte(resultData), &walksResultData)
	if err != nil {
		q.Q(err)
		foundData.Status = fmt.Sprintf("")
		return foundData
	}
	tempWalksResultData := []walkResult{}

	pgno, err := strconv.Atoi(pageNum)
	pgsize, err := strconv.Atoi(pageSize)
	totpage, err := strconv.Atoi(totalPage)

	totalRecordWant := totpage * pgsize
	if totalRecordWant > len(walksResultData) {
		totalRecordWant = len(walksResultData)
	}
	for i := 1; i <= totalRecordWant; i++ {
		tempWalksResultData = append(tempWalksResultData, walksResultData[i-1])
	}

	start, end := Paginate(pgno, pgsize, len(tempWalksResultData))
	pagedSlice := tempWalksResultData[start:end]
	b, err := json.Marshal(pagedSlice)
	if err != nil {
		q.Q(err)
		foundData.Status = fmt.Sprintf("error: %v", err)
		return foundData
	}
	paginationResultSlice := PaginationResult{
		TotalCount: len(walksResultData),
		Data:       string(b),
	}

	out, err := json.Marshal(paginationResultSlice)
	if err != nil {
		q.Q(err)
	}

	foundData.Status = "ok"
	foundData.Result = string(out)
	return foundData
}

type PaginationResult struct {
	TotalCount int    // total count of walksResultData
	Data       string // marshaled pagedSlice as string
}

func Paginate(pageNum int, pageSize int, sliceLength int) (int, int) {
	start := (pageNum - 1) * pageSize

	if start > sliceLength {
		start = sliceLength
	}

	end := start + pageSize
	if end > sliceLength {
		end = sliceLength
	}

	return start, end
}

// HandleDevices accepts devices information and stores them and can return device info
//
// POST /api/v1/devices
//
//		Example parameter: (map[string]DevInfo)
//	         {"00-60-E9-2D-91-3E": {"mac":"00-60-E9-2D-91-3E","modelname":...},
//	          "00-60-E9-1F-A6-02":{"mac":"00-60-E9-1F-A6-02","modelname":...}}
//
// GET /api/v1/devices
//
//	retrieve devices information
func HandleDevices(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		devinfo := make(map[string]DevInfo)
		err = json.Unmarshal(body, &devinfo)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		for _, v := range devinfo {
			if strings.Contains(v.Scanproto, "agent") {
				InsertAgentDevices(v)
			} else {
				InsertDev(v)
			}
		}

		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}

	devid := r.URL.Query().Get("dev")
	q.Q(devid)
	if len(devid) > 0 {
		dev, err := FindDev(devid)
		if err != nil {
			w.WriteHeader(http.StatusNotFound)
			_, err = w.Write([]byte("error: " + err.Error()))
			if err != nil {
				q.Q(err)
			}
			return
		}
		jsonBytes, err := json.Marshal(dev)
		if err != nil {
			q.Q(err)
			w.WriteHeader(http.StatusInternalServerError)
			_, err = w.Write([]byte("error: " + err.Error()))
			if err != nil {
				q.Q(err)
			}
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	specialDev := DevInfo{Mac: specialMac, Timestamp: lastTimestamp}
	QC.DevMutex.Lock()
	QC.DevData[specialMac] = specialDev
	QC.DevMutex.Unlock()
	jsonBytes, err := json.Marshal(QC.DevData)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleTopology handles the topology
//
// POST /api/v1/topology
//
//	Example parameter: map[string]Topology
//
// GET /api/v1/topology
//
//	returns topology
func HandleTopology(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		var topoinfo Topology
		err = json.Unmarshal(body, &topoinfo)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		if QC.IsRoot {
			err = InsertTopology(topoinfo)
			if err != nil {
				RespondWithError(w, err)
				return
			}
		} else {
			PublishTopology(topoinfo)
		}
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}
	// if len(QC.TopologyData) == 0 {
	// 	AddAllDeviceAsNode()
	// }
	jsonBytes, err := json.Marshal(QC.TopologyData)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleNetworkTopology handles the combined network topology data for hierarchical topology page
//
// GET /api/v1/network-topology
//
//	returns combined topology and group data optimized for NetworkTopology component
func HandleNetworkTopology(w http.ResponseWriter, r *http.Request) {
	// Ensure topology data is available
	// if len(QC.TopologyData) == 0 {
	// 	AddAllDeviceAsNode()
	// }

	// Get groups data
	relmsMap := GM.ListRelms()

	// Convert map to slice for JSON response
	var relms []Relm
	for _, relm := range relmsMap {
		relms = append(relms, *relm)
	}

	// Define types for response structure
	type Connection struct {
		Source      string `json:"source"`
		Target      string `json:"target"`
		SourcePort  string `json:"sourcePort"`
		TargetPort  string `json:"targetPort"`
		LinkType    string `json:"linkType"`
		BlockedPort string `json:"blockedPort"`
	}

	// Create subnet group topology data for easier frontend processing
	type SubnetGroupTopology struct {
		ID          string              `json:"id"`
		Name        string              `json:"name"`
		RelmName    string              `json:"relmName"`
		RegionName  string              `json:"regionName"`
		ZoneName    string              `json:"zoneName"`
		SubnetName  string              `json:"subnetName"`
		Devices     []string            `json:"devices"`
		DeviceData  map[string]Topology `json:"deviceData"`
		Connections []Connection        `json:"connections"`
	}

	// Create combined response structure
	type NetworkTopologyResponse struct {
		Groups       []Relm                `json:"groups"`
		TopologyData map[string]Topology   `json:"topologyData"`
		SubnetGroups []SubnetGroupTopology `json:"subnetGroups"`
	}

	var subnetGroups []SubnetGroupTopology

	// Process each relm to extract subnet groups with their topology data
	for _, relm := range relms {
		for _, region := range relm.Regions {
			for _, zone := range region.Zones {
				for _, subnet := range zone.Subnets {
					for _, subnetGroup := range subnet.SubnetGroups {
						sgTopology := SubnetGroupTopology{
							ID:          fmt.Sprintf("%s-%s-%s-%s-%s", relm.Name, region.Name, zone.Name, subnet.Name, subnetGroup.Name),
							Name:        subnetGroup.Name,
							RelmName:    relm.Name,
							RegionName:  region.Name,
							ZoneName:    zone.Name,
							SubnetName:  subnet.Name,
							Devices:     subnetGroup.Devices,
							DeviceData:  make(map[string]Topology),
							Connections: []Connection{},
						}

						// Get topology data for each device in the subnet group
						for _, deviceMac := range subnetGroup.Devices {
							if topologyData, exists := QC.TopologyData[deviceMac]; exists {
								sgTopology.DeviceData[deviceMac] = topologyData

								// Extract connections between devices in this subnet group
								for _, link := range topologyData.LinkData {
									// Only include connections where both source and target are in this subnet group
									if slices.Contains(subnetGroup.Devices, link.Source) && slices.Contains(subnetGroup.Devices, link.Target) {
										sgTopology.Connections = append(sgTopology.Connections, Connection{
											Source:      link.Source,
											Target:      link.Target,
											SourcePort:  link.SourcePort,
											TargetPort:  link.TargetPort,
											LinkType:    link.LinkType,
											BlockedPort: link.BlockedPort,
										})
									}
								}
							}
						}

						subnetGroups = append(subnetGroups, sgTopology)
					}
				}
			}
		}
	}

	response := NetworkTopologyResponse{
		Groups:       relms,
		TopologyData: QC.TopologyData,
		SubnetGroups: subnetGroups,
	}

	jsonBytes, err := json.Marshal(response)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// handleTopologyAction handles the save/restore/delete manual topology
//
// POST /api/v1/topology/action
//
//	Example parameter: {
//		ActionType string `json:"actionType"` can be save/restore/delete
//		Filename   string `json:"filename"`
//	}
func handleTopologyAction(w http.ResponseWriter, r *http.Request) {
	type RequestData struct {
		ActionType string `json:"actionType"`
		Filename   string `json:"filename"`
	}
	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	var reqData RequestData
	if err = json.Unmarshal(body, &reqData); err != nil {
		RespondWithError(w, err)
		return
	}
	// Define the directory and file path
	dirPath := "topology"
	fullPath := filepath.Join(dirPath, reqData.Filename)
	// Ensure the directory exists
	if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
		RespondWithError(w, err)
		return
	}

	if reqData.ActionType == "save" {
		// filter manual topology data and create data to save
		var filteredTopology []Topology
		for _, topo := range QC.TopologyData {
			if topo.TopoType == "manual" {
				filteredTopology = append(filteredTopology, topo)
			}
		}
		// Convert the struct to JSON
		jsonData, err := json.MarshalIndent(filteredTopology, "", "  ")
		if err != nil {
			RespondWithError(w, err)
			return
		}
		if err := os.WriteFile(fullPath, jsonData, os.ModePerm); err != nil {
			RespondWithError(w, err)
		}
	}
	if reqData.ActionType == "restore" {
		// Open the JSON file
		jsonData, err := os.ReadFile(fullPath)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		// Create an instance of the struct to hold the data
		var restoredTopology []Topology

		// Unmarshal the JSON data into the struct
		err = json.Unmarshal(jsonData, &restoredTopology)
		if err != nil {
			RespondWithError(w, err)
			return
		}

		// Add restored topology to QC.TopologyData
		for _, topo := range restoredTopology {
			// checking restore device topology device exist or not
			if _, err := FindDev(topo.Id); err == nil {
				QC.TopologyMutex.Lock()
				QC.TopologyData[topo.Id] = topo
				QC.TopologyMutex.Unlock()
			}
		}
	}
	if reqData.ActionType == "delete" {
		// Attempt to delete the file
		if err := os.Remove(fullPath); err != nil {
			RespondWithError(w, err)
		}
	}

	_, err = w.Write(body)
	if err != nil {
		q.Q(err)
	}
}

// HandleValidateConfig validates the configuration
// POST /api/v1/validateconfig
func HandleValidateConfig(w http.ResponseWriter, r *http.Request) {
	// Max 10MB of data
	err := r.ParseMultipartForm(10 << 20)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, "Error parsing form")
		return
	}
	// Get the file from the form data
	file, _, err := r.FormFile("configfile")
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, "Error getting file")
		return
	}
	defer file.Close()
	// Read file
	configBytes, err := io.ReadAll(file)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, "Error reading file")
		return
	}

	q.Q(string(configBytes))

	// Validate the configuration
	decryptedData, err := DecryptWithOwnPrivateKey(configBytes)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, "Error decrypting file")
		return
	}

	c := MNMSConfig{}
	err = json.Unmarshal(decryptedData, &c)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, "Error config file format is not valid")
		return
	}
	q.Q(c)
	// write MNMSConfig
	err = WriteMNMSConfig(&c)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, "Error writing config file")
	}
	// return ok
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, "Configuration is valid")
}

func UpdateClients(ci ClientInfo) (resp *http.Response, err error) {
	q.Q(ci)
	jsonBytes, err := json.Marshal(ci)
	if err != nil {
		return nil, err
	}
	return PostWithToken(QC.RootURL+"/api/v1/register",
		QC.AdminToken, bytes.NewBuffer(jsonBytes))
}

/*
HandleRegister accept client information and returns cluster client information

POST /api/v1/register

	    Example:
		{
			"Name": "client1",
			"Kind": "nms",
			"NumDevices": 0,
			"NumCmds": 0,
			"Status": "active",
			"NumDevices": 0,
			...
		}

GET /api/v1/register

	returns cluster clients information array
*/
func HandleRegister(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		ci := ClientInfo{}
		err = json.Unmarshal(body, &ci)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		q.Q(ci)
		name := ci.Name
		if name == "" {
			RespondWithError(w, fmt.Errorf("name required"))
			return
		}
		QC.ClientMutex.Lock()
		// check service already exist or not
		client, ok := QC.Clients[name]
		QC.ClientMutex.Unlock()
		if ok && client.Status == "active" {
			// Check services exists
			if client.Start != ci.Start {
				// if services exist respond with error 500
				RespondWithError(w, fmt.Errorf("service with name %s already registered", name))
				return
			}
		}

		if QC.Kind == "nms" {
			// only accept nms clients for nested nms
			if ci.Kind != "nms" {
				RespondWithError(w, fmt.Errorf("only nms clients can be registered"))
				return
			}

			// the name must contains "QC.Name/xxx"
			if !strings.HasPrefix(name, QC.Name+"/") {
				RespondWithError(w, fmt.Errorf("name must contains '%s/' when registering nested nms, for example '%s/nms1", QC.Name, QC.Name))
				return
			}
		}

		if ci.Parent == "" {
			// record the parent, can also be used to seperate name
			ci.Parent = QC.Name
		}

		if ok {
			if ci.Status == "inactive" {
				SendSyslog(LOG_ALERT, "service_inactive", fmt.Sprintf("service %s is inactive", name))
			}
			if ci.Status == "active" && client.Status == "inactive" {
				SendSyslog(LOG_ALERT, "service_active", fmt.Sprintf("service %s is reactivated", name))
			}
		}

		// add new or update
		QC.ClientMutex.Lock()
		QC.Clients[name] = ci
		QC.ClientMutex.Unlock()
		// check license client limit

		if QC.IsRoot {
			CheckQCLicense()
		} else {
			_, err = UpdateClients(ci)
			if err != nil {
				q.Q("forwarding to root failed", err)
			}
		}
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}

	// map to slice and return to avoid js "/" issue
	clients := []ClientInfo{}
	QC.ClientMutex.Lock()
	for _, v := range QC.Clients {
		clients = append(clients, v)
	}
	QC.ClientMutex.Unlock()
	jsonBytes, err := json.Marshal(clients)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleClientInfo accept clients information and returns cluster client information
//
// POST /api/v1/client
//
//	    Example parameter:
//	             { "client1": { "Name": "client1", "NumDevices": 0, ... },
//			"client2": { "Name": "client2", "NumDevices": 0, "NumCmds": 0, ...}}
//
// GET /api/v1/client
//
//	returns clients information
func HandleClientInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		clients := make(map[string]ClientInfo)
		err = json.Unmarshal(body, &clients)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		q.Q(clients)
		QC.ClientMutex.Lock()
		// clear(QC.Clients) //gives error beacuse clear function support go 1.21 or above
		QC.Clients = make(map[string]ClientInfo) // clearing QC.Client
		QC.Clients = clients
		QC.ClientMutex.Unlock()
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}
	jsonBytes, err := json.Marshal(QC.Clients)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleLogin handles login requests
//
// POST  /api/v1/login
//
//		Example parameter:
//		         {"user":"<EMAIL>","password":"Pas$word1"}
//
//		Response:
//	     need 2fa : {"sessionID": "sessionID", "user":"user1"}
//		   {"token": "AAA...", "user": "user1", "role": "admin"}
func HandleLogin(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		type loginBody struct {
			User     string `json:"user"`
			Password string `json:"password"`
		}
		var body loginBody

		err := json.NewDecoder(r.Body).Decode(&body)
		if err != nil {
			RespondWithError(w, err)
			return
		}

		token, err := generateJWT(body.User, body.Password)
		if err != nil {
			w.WriteHeader(http.StatusUnauthorized)
			_, err = w.Write([]byte(err.Error()))
			if err != nil {
				q.Q(err)
			}
			return
		}

		if !QC.IsRoot {
			if QC.Kind != "nms" {
				w.WriteHeader(http.StatusUnauthorized)
				_, err := w.Write([]byte("error: only root can issue tokens"))
				if err != nil {
					q.Q(err)
				}
			}

			if body.User != "agent" {
				w.WriteHeader(http.StatusUnauthorized)
				_, err := w.Write([]byte("error: only agent can issue tokens from nms"))
				if err != nil {
					q.Q(err)
				}
			}
		}

		user, err := GetUserConfig(body.User)
		if err != nil {
			w.WriteHeader(http.StatusUnauthorized)
			_, err = w.Write([]byte(err.Error()))
			if err != nil {
				q.Q(err)
			}
			return
		}
		res := make(map[string]interface{})
		// check 2fa
		if user.Enable2FA {
			sessionID := createLoginSession(*user)
			res["sessionID"] = sessionID
			res["user"] = user.Name
			res["email"] = user.Email
			err = json.NewEncoder(w).Encode(res)
			if err != nil {
				RespondWithError(w, err)
			}
			return
		}
		res["token"] = token
		res["user"] = body.User
		res["role"] = user.Role
		q.Q(res)
		err = json.NewEncoder(w).Encode(res)
		if err != nil {
			RespondWithError(w, err)
		}
		// w.Write([]byte(token))
		return
	}
}

type usersBody struct {
	Name     string `json:"name"`
	Email    string `json:"email"`
	Password string `json:"password"`
	Role     string `json:"role"`
}

// HandleDeleteUser handles delete user requests
//
//	 DELETE /api/v1/users
//
//	Example parameter: { "name": "abc"}
func HandleDeleteUser(w http.ResponseWriter, r *http.Request) {
	var body usersBody
	err := json.NewDecoder(r.Body).Decode(&body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()
	if !UserExist(body.Name) {
		RespondWithError(w, fmt.Errorf("user %s not exist", body.Name))
		return
	}

	err = DeleteUserConfig(body.Name)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write([]byte("ok"))
	if err != nil {
		q.Q(err)
	}
}

// HandleUpdateUser handles update user requests
//
// PUT /api/v1/users
//
//	Example parameter: { "name": "abc", "email": "<EMAIL>", "password": "password1" , "role": "admin"}
func HandleUpdateUser(w http.ResponseWriter, r *http.Request) {
	var body usersBody
	err := json.NewDecoder(r.Body).Decode(&body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()
	if !UserExist(body.Name) {
		RespondWithError(w, fmt.Errorf("user %s not exist", body.Name))
		return
	}
	err = UpdateUserConfig(body.Name, body.Role, body.Password, body.Email)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write([]byte("ok"))
	if err != nil {
		RespondWithError(w, err)
	}
	return
}

// HandleAddUser handles add user requests
//
// POST /api/v1/users
//
//	Example parameter: { "name": "abc", "email": "<EMAIL>", "password": "Pas$Word1" , "role": "admin"}
func HandleAddUser(w http.ResponseWriter, r *http.Request) {
	var body usersBody
	err := json.NewDecoder(r.Body).Decode(&body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()

	if UserExist(body.Name) {
		RespondWithError(w, fmt.Errorf("user %s already exist", body.Name))
		return
	}

	err = AddUserConfig(body.Name, body.Role, body.Password, body.Email)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write([]byte("ok"))
	if err != nil {
		q.Q(err)
	}
}

// HandleValidate2FA handles 2FA validation requests
// POST /api/v1/2fa/validate
// request :{"sessionID":"id", "code":"123456"}
// Validate 2fa code
// example response: {"valid": true, "user": "user1", "token": "token", "role": "admin""}

func HandleValidate2FA(w http.ResponseWriter, r *http.Request) {
	var data map[string]string
	err := json.NewDecoder(r.Body).Decode(&data)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()
	sessionID := data["sessionID"]
	code := data["code"]
	user, err := getLoginSession(sessionID)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	if code == "" {
		RespondWithError(w, fmt.Errorf("code is empty"))
		return
	}

	if !user.Enable2FA {
		RespondWithError(w, fmt.Errorf("2fa not enabled"))
		return
	}
	secret := user.Secret

	valid := totp.Validate(code, secret)
	q.Q(code, secret, valid)
	var token string
	if !valid {

		w.WriteHeader(http.StatusUnauthorized)
		_, _ = w.Write([]byte("invalid code"))
		return

	}
	token, err = generateJWT(user.Name, user.Password)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	res := make(map[string]interface{})
	res["user"] = user.Name
	res["token"] = token
	res["role"] = user.Role
	err = json.NewEncoder(w).Encode(res)
	if err != nil {
		RespondWithError(w, err)
	}
	return
}

// HandleInfo handles system informaton requests
// GET /api/v1/info
// Get system information
// response {QA objcet}
func HandleInfo(w http.ResponseWriter, r *http.Request) {
	// GET

	qcBytes, err := MarshalQC()
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(qcBytes)
	if err != nil {
		RespondWithError(w, err)
	}
	return
}

// HandleLicenseInfo GET current license information
// GET /api/v1/license-info
// GET license information
// response {NimbleLicense}
func HandleLicenseInfo(w http.ResponseWriter, r *http.Request) {
	licJSON, err := json.Marshal(QC.License)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(licJSON)
	if err != nil {
		RespondWithError(w, err)
	}
	return
}

// Handle2FA handles 2FA requests
// GET /api/v1/2fa/secret?user=user1
// get current user's 2fa secret
// response : {"user":"user1", "secret":"secret"}
//
// POST /api/v1/2fa/secret
// request body {"user":"user1"}
// response : {"user":"user1", "secret":"secret"}
// Generate 2fa secret
//
// PUT /api/v1/2fa/secret
// request body {"user":"user1"}
// Update 2fa secret
//
// DELETE /api/v1/2fa/secret
// request body {"user":"user1"}
// Disable user's 2fa
func Handle2FA(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		userID := r.URL.Query().Get("user")
		if userID == "" {
			RespondWithError(w, fmt.Errorf("user is empty"))
			return
		}
		user, err := GetUserConfig(userID)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		if !user.Enable2FA {
			RespondWithError(w, fmt.Errorf("2fa not enabled"))
			return
		}
		secret := user.Secret
		res := make(map[string]interface{})
		res["user"] = userID
		res["secret"] = secret
		res["account"] = user.Email
		res["issuer"] = IssuerOf2FA
		res["enable2fa"] = user.Enable2FA
		err = json.NewEncoder(w).Encode(res)
		if err != nil {
			RespondWithError(w, err)
		}
		return
	}

	if r.Method == "POST" {
		var data map[string]string
		err := json.NewDecoder(r.Body).Decode(&data)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()
		userID := data["user"]

		user, err := GetUserConfig(userID)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		if user.Enable2FA {
			RespondWithError(w, fmt.Errorf("2fa already enabled, use PUT to update"))
			return
		}
		if len(user.Email) == 0 {
			RespondWithError(w, fmt.Errorf("email is empty"))
			return
		}
		// generate 2fa secret
		secret, err := generate2FASecret(user.Email)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		// save 2fa secret
		user.Secret = secret
		user.Enable2FA = true
		err = MergeUserConfig(*user)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		// write response
		res := make(map[string]interface{})
		res["secret"] = secret
		res["account"] = user.Email
		res["issuer"] = IssuerOf2FA
		res["user"] = userID
		err = json.NewEncoder(w).Encode(res)
		if err != nil {
			RespondWithError(w, err)
		}
		return
	}

	if r.Method == "PUT" {
		var data map[string]string
		err := json.NewDecoder(r.Body).Decode(&data)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()
		userID := data["user"]
		user, err := GetUserConfig(userID)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		if !user.Enable2FA {
			RespondWithError(w, fmt.Errorf("2fa not enabled"))
			return
		}
		if len(user.Email) == 0 {
			RespondWithError(w, fmt.Errorf("email is empty"))
			return
		}
		// generate 2fa secret
		secret, err := generate2FASecret(user.Email)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		// save 2fa secret
		user.Secret = secret
		err = MergeUserConfig(*user)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		// write response
		res := make(map[string]interface{})
		res["secret"] = secret
		res["account"] = user.Email
		res["issuer"] = IssuerOf2FA
		res["user"] = userID
		err = json.NewEncoder(w).Encode(res)
		if err != nil {
			RespondWithError(w, err)
		}
		return
	}
	if r.Method == "DELETE" {
		// disable 2fa and delete secret
		var data map[string]string
		err := json.NewDecoder(r.Body).Decode(&data)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()
		userID := data["user"]
		user, err := GetUserConfig(userID)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		if !user.Enable2FA {
			RespondWithError(w, fmt.Errorf("2fa not enabled"))
			return
		}
		user.Enable2FA = false
		user.Secret = ""
		err = MergeUserConfig(*user)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		// write response
		_, err = w.Write([]byte("ok"))
		if err != nil {
			RespondWithError(w, err)
		}
		return
	}
}

// HandleUsers handles users requests
//
// GET  /api/v1/users
//
//	Example return: { "name": "abc", "email": "<EMAIL>", "password": "pass1" , "role": "admin"}
func HandleUsers(w http.ResponseWriter, r *http.Request) {
	// get mnms config
	c, err := GetMNMSConfig()
	if err != nil {
		RespondWithError(w, err)
		return
	}

	// hash password
	for k, v := range c.Users {
		v.Password = "#####"
		c.Users[k] = v
	}

	err = json.NewEncoder(w).Encode(c.Users)
	if err != nil {
		RespondWithError(w, err)
		return
	}
}

// HandleLogs accepts log messages for storage in memory and returns them
//
// POST /api/v1/logs
//
//	Example paramter: (map[string]Log)
//
// GET /api/v1/logs
//
//	returns logs from memory
func HandleLogs(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		logs := make(map[string]Log)
		err = json.Unmarshal(body, &logs)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		for _, v := range logs {
			InsertLogKind(&v)
		}
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}
	jsonBytes, err := json.Marshal(QC.Logs)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleLocalSyslogs handles syslogs requests

GET /api/v1/syslogs

	query parameters:
	start: start time, default is 1970/01/01 00:00:00
	end: end time, default is now
	number: number of logs to return, default is -1, which means all logs

	Example:
	/api/v1/syslogs?start=2023/02/21 22:06:00&end=2023/02/23 22:08:00&number=3

	response:
	returns local syslogs in json format
*/
func HandleLocalSyslogs(w http.ResponseWriter, r *http.Request) {
	start := r.URL.Query().Get("start")
	end := r.URL.Query().Get("end")
	n := r.URL.Query().Get("number")

	number, err := strconv.Atoi(n)
	if err != nil {
		number = -1
	}

	if start == "" {
		start = time.Unix(0, 0).Format(syslog_time_format)
	}

	if end == "" {
		end = time.Now().Format(syslog_time_format)
	}

	logs, err := getSyslogsWithTime([]string{QC.SyslogLocalPath}, start, end, number)
	if err != nil {
		_, err = w.Write([]byte(""))
		if err != nil {
			q.Q(err)
		}
		return
	}

	jsonBytes, err := json.Marshal(&logs)
	if err != nil {
		_, err = w.Write([]byte(""))
		if err != nil {
			q.Q(err)
		}
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleAlertSyslogs handles syslogs requests

GET /api/v1/syslogs/alert

	response:
	returns local alert syslogs in json format
*/
func HandleAlertSyslogs(w http.ResponseWriter, r *http.Request) {
	if !QC.IsRoot {
		_, err := w.Write([]byte("error: this api requires root service"))
		if err != nil {
			q.Q(err)
		}
		return
	}

	start := time.Unix(0, 0).Format(syslog_time_format)
	end := time.Now().Format(syslog_time_format)
	number := -1
	logs, err := getSyslogsWithTime([]string{QC.SyslogLocalPath}, start, end, number)
	if err != nil {
		_, err = w.Write([]byte(""))
		if err != nil {
			q.Q(err)
		}
		return
	}
	// 30 historical alert messages
	type alertMessage struct {
		Kind      string `json:"kind"`
		Level     int    `json:"level"`
		Message   string `json:"message"`
		Timestamp int    `json:"timestamp"`
	}
	var alertMsgs []alertMessage
	count := 0
	for _, v := range logs {
		if count >= 30 {
			break
		}
		timestamp := *v.Timestamp
		if int(*v.Severity) < 2 {
			ix := strings.Index(*v.Message, ">")
			message := alertMessage{
				Kind:      "nimbl_syslog",
				Level:     int(*v.Severity),
				Message:   strings.TrimSpace((*v.Message)[ix+1:]),
				Timestamp: int(timestamp.UnixMilli()),
			}
			alertMsgs = append(alertMsgs, message)
			// q.Q(*v.Severity, *v.Message)
			count++
		}
	}

	jsonBytes, err := json.Marshal(&alertMsgs)
	if err != nil {
		_, err = w.Write([]byte(""))
		if err != nil {
			q.Q(err)
		}
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleWireguard handles wireguard requests

POST /api/v1/wg

	accepts wireguard data, including config and status

GET /api/v1/wg

	retrieves wireguard data, including config and status

Get /api/v1/wg?name=[name]

	retrieves wireguard data for a specific mnms name
*/
func HandleWireguard(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		data := make(map[string]WgInfo)
		err = json.Unmarshal(body, &data)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		QC.WgMutex.Lock()
		for k, v := range data {
			QC.WgData[k] = v
		}
		QC.WgMutex.Unlock()
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}

	// Get
	name := r.URL.Query().Get("name")
	wgData := make(map[string]WgInfo)
	if name != "" {
		QC.WgMutex.Lock()
		found, ok := QC.WgData[name]
		QC.WgMutex.Unlock()
		if ok {
			wgData[name] = found
		}
		jsonBytes, err := json.Marshal(wgData)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	QC.WgMutex.Lock()
	wgData = QC.WgData
	QC.WgMutex.Unlock()
	statusInfo, err := WgStatus()
	if err != nil {
		q.Q(err)
	}
	info, ok := wgData[QC.Name]
	if !ok {
		jsonBytes, err := json.Marshal(wgData)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	info.Status = statusInfo
	wgData[QC.Name] = info
	jsonBytes, err := json.Marshal(wgData)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleTcpProxy handles tcp proxy requests

POST /api/v1/tcpproxy

	client sends a json object with the following format:
	{
		"client1": {
			":9001" : {
				"from": ":9001",
				"to": "***********00:443"
			}
		}
	}

GET /api/v1/tcpproxy

	retrieve all port proxying
	Example return:
	{
		"client1": {
			":9001" : {
				"from": ":9001",
				"to": "***********00:443"
			},
			":9002" : {
				"from": ":9002",
				"to": "***********01:443"
			}
		},
		"client2": {
			":9001" : {
				"from": ":9001",
				"to": "192.168.5.100:80"
			}
		}
	}

GET /api/v1/tcpproxy?name=[name]

	retrieve all port proxying for a specific mnms name
*/
func HandleTcpProxy(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		// client post TcpProxyData to root
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		proxies := make(map[string]map[string]TcpProxyInfo)
		err = json.Unmarshal(body, &proxies)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		QC.TcpProxyMutex.Lock()
		for k, v := range proxies {
			QC.TcpProxyData[k] = v
		}
		QC.TcpProxyMutex.Unlock()
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}

	// Get
	name := r.URL.Query().Get("name")
	proxyData := make(map[string]map[string]TcpProxyInfo)
	if name != "" {
		QC.TcpProxyMutex.Lock()
		found, ok := QC.TcpProxyData[name]
		QC.TcpProxyMutex.Unlock()
		if ok {
			proxyData[name] = found
		}
		jsonBytes, err := json.Marshal(proxyData)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}

	QC.TcpProxyMutex.Lock()
	proxyData = QC.TcpProxyData
	QC.TcpProxyMutex.Unlock()
	jsonBytes, err := json.Marshal(proxyData)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleAgentOpenvpnKeys handles agent openvpn keys
GET /api/v1/agent/openvpn/keys

	retrieve status of agent openvpn keys

POST /api/v1/agent/openvpn/keys

	agent sends a json object with agent openvpn keys
*/
func HandleAgentOpenvpnKeys(w http.ResponseWriter, r *http.Request) {
	// enableCors(&w)
	if r.Method == "POST" {
		bodyText, err := io.ReadAll(r.Body)
		if err != nil {
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()

		if !QC.IsRoot && QC.RootURL != "" {
			// forward request to root
			resp, err := PostWithToken(QC.RootURL+"/api/v1/agent/openvpn/keys", QC.AdminToken, bytes.NewBuffer(bodyText))
			if err != nil {
				RespondWithError(w, err)
				return
			}
			defer resp.Body.Close()

			_, err = io.Copy(w, resp.Body)
			if err != nil {
				q.Q(err)
			}
			return
		}

		openvpnData := make(map[string]OpenvpnKeys)
		err = json.Unmarshal(bodyText, &openvpnData)
		if err != nil {
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		for k, v := range openvpnData {
			// fill in missing timestamp
			if v.Timestamp == "" {
				v.Timestamp = time.Now().Format(time.RFC3339)
			}
			QC.OpenvpnMutex.Lock()
			QC.OpenvpnData[k] = v
			QC.OpenvpnMutex.Unlock()
		}
		_, err = w.Write(bodyText)
		if err != nil {
			q.Q(err)
		}
		return
	}
	// GET
	openvpnClientName := r.URL.Query().Get("client")

	if openvpnClientName != "" {
		openvpnData := make(map[string]OpenvpnKeys)
		q.Q("get openvpn data")
		QC.OpenvpnMutex.Lock()
		for k, v := range QC.OpenvpnData {
			if strings.Compare(k, openvpnClientName) == 0 {
				openvpnData[k] = v
			}
		}
		QC.OpenvpnMutex.Unlock()
		jsonBytes, err := json.Marshal(openvpnData)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
}

/*
HandleAgentVersion handles agent version
POST /api/v1/agent/version

	agent sends a json object with agent version
	Example parameter:
				{"version":"1.0.1"}
	Response:
				{"version": "1.0.4"}
*/
func HandleAgentVersion(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		var body AgentMessages

		err := json.NewDecoder(r.Body).Decode(&body)
		if err != nil {
			RespondWithError(w, err)
			return
		}

		// check agent version
		status := CheckBbnimAndAgentVersion(QC.Version, body.Version)
		if status != nil {
			q.Q(status)
		}

		var resp AgentMessages
		resp.Version = QC.Version
		q.Q(resp)
		jsonBytes, err := json.Marshal(resp)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
}

/*
HandleAgentFirmware handles agent get firmware file
POST /api/v1/agent/firmware

	agent sends a json object with url
	Example parameter:
			{"url":"http://***********42:27183/api/v1/files/XXXX.dld"}
	Response:
			report file content
*/
func HandleAgentFirmware(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		type urlBody struct {
			Url string `json:"url"`
		}
		var body urlBody

		err := json.NewDecoder(r.Body).Decode(&body)
		if err != nil {
			RespondWithError(w, err)
			return
		}

		var jsonBytes []byte
		jsonBytes, err = ReadFirmwareFile(body.Url)
		if err != nil {
			RespondWithError(w, err)
			return
		}

		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
}

/*
HandleAgentPort handles port and power information from agent
POST /api/v1/agent/ports

	agent sends a json object with port and power information
	Example parameter:
	{
		"11:22:33:44:55:66": {
			"portStatus": [{
				"portName": "Port 1",
				"portStatus": true,
				"speed": "57485",
				"portMode": "copper",
				"inOctets": "12959",
				"inErrors": "0",
				"inUcastPkts": "68408",
				"inMulticastPkts": "84527",
				"inBroadcastPkts": "42296",
				"outOctets": "52187",
				"outErrors": "74505",
				"outUcastPkts": "78895",
				"outMulticastPkts": "83914",
				"outBroadcastPkts": "55815",
				"enableStatus": true
			},
			// ... more port information
			],
			"powerStatus": [{
				"powerId": "1",
				"status": true
			},
			{
				"powerId": "2",
				"status": true
			}
			// ... if other power information
			]
		}
	}
*/
func HandleAgentPort(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		portAndPowerInfo := make(map[string]PortAndPowerInfo)
		err = json.Unmarshal(body, &portAndPowerInfo)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		if QC.IsRoot {
			InsertPortAndPower(portAndPowerInfo)
		} else {
			PublishPortAndPower(&portAndPowerInfo)
		}
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}
	// Get
	QC.PortAndPowerMutex.Lock()
	defer QC.PortAndPowerMutex.Unlock()
	portAndPowerInfo := QC.PortAndPowerInfo
	jsonBytes, err := json.Marshal(portAndPowerInfo)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

func HandleServiceUpdate(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		type ServiceVersion struct {
			Version string `json:"version"`
			Status  string `json:"status"`
		}
		if !QC.IsRoot && QC.RootURL != "" {
			body, err := io.ReadAll(r.Body)
			if err != nil {
				RespondWithError(w, err)
				return
			}
			// forward request to root
			resp, err := PostWithToken(QC.RootURL+"/api/v1/service/update", QC.AdminToken, bytes.NewBuffer(body))
			if err != nil {
				RespondWithError(w, err)
				return
			}
			defer resp.Body.Close()
			_, err = io.Copy(w, resp.Body)
			if err != nil {
				q.Q(err)
			}
			return
		}
		if QC.IsRoot {
			body, err := io.ReadAll(r.Body)
			if err != nil {
				RespondWithError(w, err)
				return
			}
			var req ServiceVersion
			err = json.Unmarshal(body, &req)
			if err != nil {
				RespondWithError(w, err)
				return
			}

			rootVersion, status := CheckServiceVersion(req.Version)
			req.Status = status
			req.Version = rootVersion
			jsonBytes, err := json.Marshal(req)
			if err != nil {
				RespondWithError(w, err)
				return
			}
			_, err = w.Write(jsonBytes)
			if err != nil {
				q.Q(err)
			}
		}
		return
	}
	// Get
	if !QC.IsRoot && QC.RootURL != "" {
		resp, err := GetWithToken(QC.RootURL+"/api/v1/service/update", QC.AdminToken)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		defer resp.Body.Close()
		_, err = io.Copy(w, resp.Body)
		if err != nil {
			q.Q(err)
		}
		return
	}
	data, err := DownloadFwFromCloud()
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(data)
	if err != nil {
		q.Q(err)
	}
	return
}

// HandleSshTunnels handles ssh tunnels requests

// POST /api/v1/ssh/tunnels
//
//	device send register tunnel request, the request body is a json object with the following format:
//
//	{
//		"mac": "mac address",
//		"nms_url": "http://nms_url",
//	}
//
//	response:
//	{
//		"status": "ok",
//		"root_host": "*******",
//		"ssh_port": 22,
//		"listen_port": 34567,
//		"message": ""
//	}
//
//	Note that in order to have the same scenario as TCP mode which communication is between device and nms not root,
//	nms will bypass the request to root and return the response to device.
//
// GET /api/v1/ssh/tunnels
//
//	retrieves ssh tunnel data
func HandleSshTunnels(w http.ResponseWriter, r *http.Request) {
	QC.SshConnectionsMutex.Lock()
	defer QC.SshConnectionsMutex.Unlock()
	// post
	if r.Method == "POST" {
		type Request struct {
			Mac      string `json:"mac"`
			NmsURL   string `json:"nms_url"`
			RootHost string `json:"root_host"`
		}
		if !QC.IsRoot {
			if len(QC.RootURL) == 0 {
				RespondWithError(w, fmt.Errorf("error: root url is empty"))
				return
			}

			// parse request
			var req Request
			err := json.NewDecoder(r.Body).Decode(&req)
			if err != nil {
				RespondWithError(w, err)
				return
			}
			defer r.Body.Close()
			// add root host to request since only nmssvc has the root url
			rHost, err := LookupRootHost(req.NmsURL)
			if err != nil {
				RespondWithError(w, err)
				return
			}
			req.RootHost = rHost

			// marshal request
			jsonBytes, err := json.Marshal(req)
			if err != nil {
				RespondWithError(w, err)
				return
			}

			// forward request to root
			resp, err := PostWithToken(QC.RootURL+"/api/v1/ssh/tunnels", QC.AdminToken, bytes.NewBuffer(jsonBytes))
			if err != nil {
				RespondWithError(w, err)
				return
			}
			defer resp.Body.Close()

			// forward response to device
			_, err = io.Copy(w, resp.Body)
			if err != nil {
				q.Q(err)
			}
			return
		}

		var devReq Request
		err := json.NewDecoder(r.Body).Decode(&devReq)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()

		info := AutoRunSshInfo()
		if info.Status == "ok" {
			info.RootHost = devReq.RootHost
		}

		err = ManagePort("allow", strconv.Itoa(info.ListenPort), "")
		if err != nil {
			q.Q("failed to allow port automatically", info.ListenPort, err)
			info.Message = "warning: port " + strconv.Itoa(info.ListenPort) + " was not automatically allowed, please ensure the port is manually allowed."
		}

		jsonBytes, err := json.Marshal(info)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}

		return
	}

	// get
	jsonBytes, err := json.Marshal(QC.SshConnections)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleMachinesServices returns a json object with the pairing of machine_id, , bblogsvcs, bbnmssvc, bbidpsvc, and ip_addresses.
So that the user can see which machine has logsvcs .
GET /api/v1/machines/services

Query Parameters:

	returns a json object with the following format:
	{
		"machine_id": {
			"bbnmssvcs": ["service1", "service2"],
			"bblogsvcs": ["service5", "service6"],
			"bbidpsvcs": ["service7", "service8"],
			"ip_addresses": ["ip1", "ip2"],
			"machine_id": "machine_id"
		}
	}

	example:

	{
		"2b1b1c2b-af08-4318-9886-5c087d00a81e": {
			"bbnmssvcs": [
				"nms"
			],
			"machine_id": "2b1b1c2b-af08-4318-9886-5c087d00a81e",
			"ip_addresses": [
				"***********"
			]
		}
	}
*/
func HandleMachinesServices(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		RespondWithError(w, fmt.Errorf("error: method not allowed"))
		return
	}

	type PairsResp struct {
		BBNmssvcs   []string `json:"bbnmssvcs"`
		BBLogsvcs   []string `json:"bblogsvcs"`
		BBIdpsvcs   []string `json:"bbidpsvcs"`
		MachineID   string   `json:"machine_id"`
		IPAddresses []string `json:"ip_addresses"`
	}

	QC.ClientMutex.Lock()
	defer QC.ClientMutex.Unlock()

	pairs := make(map[string]PairsResp)

	for _, client := range QC.Clients {
		// check
		pair, ok := pairs[client.MachineID]
		if !ok {
			pair = PairsResp{
				MachineID:   client.MachineID,
				IPAddresses: client.IPAddresses,
			}
		}

		if client.Kind == "syslog" {
			pair.BBLogsvcs = append(pair.BBLogsvcs, client.Name)
		} else if client.Kind == "idps" {
			pair.BBIdpsvcs = append(pair.BBIdpsvcs, client.Name)
		} else if client.Kind == "nms" {
			pair.BBNmssvcs = append(pair.BBNmssvcs, client.Name)
		}

		pairs[client.MachineID] = pair
	}

	jsonBytes, err := json.Marshal(pairs)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

/*
HandleForwards handles forward services data
GET /api/v1/forwards
returns a json object with the following format:

	{
		"forward-1": {
			"whatsapp" : {
				"enabled": true,
				"account_sid": "ACXXXXXXXXXXXXXXXX"
				"auth_token": "your_auth_token",
				"from_number": "+*********0",
				"to_numbers": [
					"+**********",
				],
				"alert_config": {
					"min_severity": 0,
					"max_severity": 5,
					"rate_limit_seconds": 300,
					"max_alerts_per_minute": 5,
					"keywords": [],
					"exclude_keywords": []
				}
			}
			"telegram" : {
				"enabled": true,
				"bot_token": "*********:ABCDEFghijklmnopqrstuvwxyz",
				"chat_ids": [
					"100*********0",
				],
				"alert_config": {...}
			}
			"mqtt" : {
				"enabled": true,
				"broker_host": "tcp://mqtt.example.com",
				"broker_port": 1883,
				"username": "",
				"password": "",
				"topic": "mnms/alerts",
				"qos": 0,
				"retain": false,
				"alert_config": {...}
			}
		},
		"forward-2": {
			"whatsapp" : {},
			...
		}
	}

GET /api/v1/forwards?name=[name]
returns a json object with the forwards data for a specific mnms name
Example return for GET /api/v1/forwards?name=forward-1

	{
		"forward-1": {...}
	}

POST /api/v1/forwards
accepts a json object with the same format as above, and updates the forwards data
*/
func HandleForwards(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		// forward svc to root
		if !QC.IsRoot {
			RespondWithError(w, fmt.Errorf("error: this api requires root service"))
		}

		configs := make(map[string]ForwardConfig)
		err := json.NewDecoder(r.Body).Decode(&configs)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()

		if len(configs) == 0 {
			RespondWithError(w, fmt.Errorf("error: no forward configs provided"))
			return
		}

		QC.ForwardMutex.Lock()
		defer QC.ForwardMutex.Unlock()
		for k, v := range configs {
			QC.ForwardSvcData[k] = v
		}

		// return ok
		_, err = w.Write([]byte("ok"))
		if err != nil {
			q.Q(err)
		}

		return
	}

	// GET
	QC.ForwardMutex.Lock()
	defer QC.ForwardMutex.Unlock()

	forwardData := make(map[string]ForwardConfig)
	name := r.URL.Query().Get("name")
	if name != "" {
		found, ok := QC.ForwardSvcData[name]
		if ok {
			forwardData[name] = found
		}
	} else {
		forwardData = QC.ForwardSvcData
	}

	jsonBytes, err := json.Marshal(forwardData)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

type staticFileSystem struct {
	http.FileSystem
}

func newStaticFileSystem() *staticFileSystem {
	sub, err := fs.Sub(staticFS, "dist")
	if err != nil {
		panic(err)
	}

	return &staticFileSystem{
		FileSystem: http.FS(sub),
	}
}

func (s *staticFileSystem) exists(prefix string, path string) bool {
	buildpath := fmt.Sprintf("dist%s", path)

	// support for folders
	if strings.HasSuffix(path, "/") {
		_, err := staticFS.ReadDir(strings.TrimSuffix(buildpath, "/"))
		return err == nil
	}

	// support for files
	f, err := staticFS.Open(buildpath)
	if f != nil {
		_ = f.Close()
	}
	return err == nil
}

// HandleListGroups lists all groups (relms)
//
// GET /api/v1/groups
//
//	returns all relms with cleanup of non-existent devices
func HandleListGroups(w http.ResponseWriter, r *http.Request) {
	relms := GM.ListRelms()

	jsonBytes, err := json.Marshal(relms)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleCleanupGroups manually cleans up non-existent devices from all subnet groups
//
// POST /api/v1/groups/cleanup
//
//	returns cleanup statistics
func HandleCleanupGroups(w http.ResponseWriter, r *http.Request) {
	hasChanges := GM.CleanupNonExistentDevices()

	response := map[string]interface{}{
		"success":    true,
		"message":    "Device cleanup completed",
		"hasChanges": hasChanges,
	}

	if hasChanges {
		response["message"] = "Non-existent devices removed from subnet groups"
	} else {
		response["message"] = "No cleanup needed - all devices in subnet groups exist"
	}

	jsonBytes, err := json.Marshal(response)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleCreateGroup creates a new group (relm)
//
// POST /api/v1/groups
//
//	Example parameter: {"name": "my-relm", "comment": "test relm", "location": "Indonesia"}
func HandleCreateGroup(w http.ResponseWriter, r *http.Request) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()

	var relm Relm
	err = json.Unmarshal(body, &relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	if relm.Name == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	// Validate the relm structure
	err = ValidateRelm(&relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	err = GM.CreateRelm(&relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	jsonBytes, err := json.Marshal(relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleGetGroup retrieves a specific group (relm)
//
// GET /api/v1/groups/{relmName}
//
//	returns the specified relm with cleanup of non-existent devices
func HandleGetGroup(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	relm, err := GM.GetRelm(relmName)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	jsonBytes, err := json.Marshal(relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleUpdateGroup updates an existing group (relm)
//
// PUT /api/v1/groups/{relmName}
//
//	Example parameter: {"name": "my-relm", "comment": "updated comment", "location": "Indonesia", "image": "base64-image-data"}
func HandleUpdateGroup(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()

	var relm Relm
	err = json.Unmarshal(body, &relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	// Ensure the name matches the URL parameter
	relm.Name = relmName

	// Validate the relm structure
	err = ValidateRelm(&relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	err = GM.UpdateRelm(&relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	jsonBytes, err := json.Marshal(relm)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleDeleteGroup deletes a group (relm)
//
// DELETE /api/v1/groups/{relmName}
//
//	deletes the specified relm
func HandleDeleteGroup(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	err := GM.DeleteRelm(relmName)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "relm deleted successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleDeleteRegion deletes a region and all its zones and subnets
//
// DELETE /api/v1/groups/{relmName}/regions/{regionName}
//
//	deletes the specified region and all its child zones and subnets
func HandleDeleteRegion(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")

	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	if regionName == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	err := GM.DeleteRegion(relmName, regionName)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "region deleted successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleDeleteZone deletes a zone and all its subnets
//
// DELETE /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}
//
//	deletes the specified zone and all its child subnets
func HandleDeleteZone(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")

	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	if regionName == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	if zoneName == "" {
		RespondWithError(w, fmt.Errorf("zone name is required"))
		return
	}

	err := GM.DeleteZone(relmName, regionName, zoneName)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "zone deleted successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleDeleteSubnet deletes a subnet and removes all its devices
//
// DELETE /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}
//
//	deletes the specified subnet and removes all its devices
func HandleDeleteSubnet(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")
	subnetName := chi.URLParam(r, "subnetName")

	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	if regionName == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	if zoneName == "" {
		RespondWithError(w, fmt.Errorf("zone name is required"))
		return
	}

	if subnetName == "" {
		RespondWithError(w, fmt.Errorf("subnet name is required"))
		return
	}

	err := GM.DeleteSubnet(relmName, regionName, zoneName, subnetName)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "subnet deleted successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleAddSubnetGroup adds a subnet group to an existing subnet
//
// POST /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups
//
//	Example parameter: {"name": "group1", "comment": "subnet group 1"}
func HandleAddSubnetGroup(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")
	subnetName := chi.URLParam(r, "subnetName")

	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	if regionName == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	if zoneName == "" {
		RespondWithError(w, fmt.Errorf("zone name is required"))
		return
	}

	if subnetName == "" {
		RespondWithError(w, fmt.Errorf("subnet name is required"))
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	var subnetGroupData SubnetGroup
	err = json.Unmarshal(body, &subnetGroupData)
	if err != nil {
		RespondWithError(w, fmt.Errorf("invalid JSON: %v", err))
		return
	}

	err = GM.AddSubnetGroup(relmName, regionName, zoneName, subnetName, subnetGroupData)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "subnet group added successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleDeleteSubnetGroup deletes a subnet group and removes all its devices
//
// DELETE /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups/{subnetGroupName}
//
//	deletes the specified subnet group and removes all its devices
func HandleDeleteSubnetGroup(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")
	subnetName := chi.URLParam(r, "subnetName")
	subnetGroupName := chi.URLParam(r, "subnetGroupName")

	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	if regionName == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	if zoneName == "" {
		RespondWithError(w, fmt.Errorf("zone name is required"))
		return
	}

	if subnetName == "" {
		RespondWithError(w, fmt.Errorf("subnet name is required"))
		return
	}

	if subnetGroupName == "" {
		RespondWithError(w, fmt.Errorf("subnet group name is required"))
		return
	}

	err := GM.DeleteSubnetGroup(relmName, regionName, zoneName, subnetName, subnetGroupName)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "subnet group deleted successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleAddDeviceToSubnetGroup adds a device to a subnet group
//
// POST /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups/{subnetGroupName}/devices
//
//	Example parameter: {"deviceMac": "AA-BB-CC-DD-EE-FF"}
func HandleAddDeviceToSubnetGroup(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")
	subnetName := chi.URLParam(r, "subnetName")
	subnetGroupName := chi.URLParam(r, "subnetGroupName")

	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	if regionName == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	if zoneName == "" {
		RespondWithError(w, fmt.Errorf("zone name is required"))
		return
	}

	if subnetName == "" {
		RespondWithError(w, fmt.Errorf("subnet name is required"))
		return
	}

	if subnetGroupName == "" {
		RespondWithError(w, fmt.Errorf("subnet group name is required"))
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	var deviceData struct {
		DeviceMac string `json:"deviceMac"`
	}
	err = json.Unmarshal(body, &deviceData)
	if err != nil {
		RespondWithError(w, fmt.Errorf("invalid JSON: %v", err))
		return
	}

	err = GM.AddDeviceToSubnetGroup(relmName, regionName, zoneName, subnetName, subnetGroupName, deviceData.DeviceMac)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "device added to subnet group successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleRemoveDeviceFromSubnetGroup removes a device from a subnet group
//
// DELETE /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/subnetgroups/{subnetGroupName}/devices/{deviceMac}
//
//	removes the specified device from the subnet group
func HandleRemoveDeviceFromSubnetGroup(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")
	subnetName := chi.URLParam(r, "subnetName")
	subnetGroupName := chi.URLParam(r, "subnetGroupName")
	deviceMac := chi.URLParam(r, "deviceMac")

	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	if regionName == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	if zoneName == "" {
		RespondWithError(w, fmt.Errorf("zone name is required"))
		return
	}

	if subnetName == "" {
		RespondWithError(w, fmt.Errorf("subnet name is required"))
		return
	}

	if subnetGroupName == "" {
		RespondWithError(w, fmt.Errorf("subnet group name is required"))
		return
	}

	if deviceMac == "" {
		RespondWithError(w, fmt.Errorf("device MAC is required"))
		return
	}

	err := GM.RemoveDeviceFromSubnetGroup(relmName, regionName, zoneName, subnetName, subnetGroupName, deviceMac)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write([]byte(`{"message": "device removed from subnet group successfully"}`))
	if err != nil {
		q.Q(err)
	}
}

// HandleAddRegion adds a region to an existing relm
//
// POST /api/v1/groups/{relmName}/regions
//
//	Example parameter: {"name": "east-1", "comment": "east region"}
func HandleAddRegion(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	if relmName == "" {
		RespondWithError(w, fmt.Errorf("relm name is required"))
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()

	var region Region
	err = json.Unmarshal(body, &region)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	if region.Name == "" {
		RespondWithError(w, fmt.Errorf("region name is required"))
		return
	}

	// Validate the region structure
	err = ValidateRegion(&region)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	err = GM.AddRegionToRelm(relmName, region)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	jsonBytes, err := json.Marshal(region)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleAddZone adds a zone to an existing region
//
// POST /api/v1/groups/{relmName}/regions/{regionName}/zones
//
//	Example parameter: {"name": "east-1-a", "comment": "main zone"}
func HandleAddZone(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")

	if relmName == "" || regionName == "" {
		RespondWithError(w, fmt.Errorf("relm name and region name are required"))
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()

	var zone Zone
	err = json.Unmarshal(body, &zone)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	if zone.Name == "" {
		RespondWithError(w, fmt.Errorf("zone name is required"))
		return
	}

	// Validate the zone structure
	err = ValidateZone(&zone)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	err = GM.AddZoneToRegion(relmName, regionName, zone)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	jsonBytes, err := json.Marshal(zone)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleAddSubnet adds a subnet to an existing zone
//
// POST /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets
//
//	Example parameter: {"name": "east-1-a-subnet-1", "comment": "main subnet", "devices": ["00-60-E9-2D-91-3E"]}
func HandleAddSubnet(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")

	if relmName == "" || regionName == "" || zoneName == "" {
		RespondWithError(w, fmt.Errorf("relm name, region name, and zone name are required"))
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()

	var subnet Subnet
	err = json.Unmarshal(body, &subnet)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	if subnet.Name == "" {
		RespondWithError(w, fmt.Errorf("subnet name is required"))
		return
	}

	// Validate the subnet structure
	err = ValidateSubnet(&subnet)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	err = GM.AddSubnetToZone(relmName, regionName, zoneName, subnet)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	jsonBytes, err := json.Marshal(subnet)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleAddDeviceToSubnet adds a device to an existing subnet
//
// POST /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/devices
//
//	Example parameter: {"deviceMac": "00-60-E9-2D-91-3E"}
func HandleAddDeviceToSubnet(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")
	subnetName := chi.URLParam(r, "subnetName")

	if relmName == "" || regionName == "" || zoneName == "" || subnetName == "" {
		RespondWithError(w, fmt.Errorf("relm name, region name, zone name, and subnet name are required"))
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	defer r.Body.Close()

	var deviceRequest struct {
		DeviceMac string `json:"deviceMac"`
	}
	err = json.Unmarshal(body, &deviceRequest)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	if deviceRequest.DeviceMac == "" {
		RespondWithError(w, fmt.Errorf("device MAC address is required"))
		return
	}

	// Validate MAC address format
	if !isValidMacAddress(deviceRequest.DeviceMac) {
		RespondWithError(w, fmt.Errorf("invalid MAC address format: %s", deviceRequest.DeviceMac))
		return
	}

	// Optionally validate that device exists in the system
	err = ValidateDeviceExists(deviceRequest.DeviceMac)
	if err != nil {
		q.Q("Warning: Device not found in system:", err)
		// Continue anyway - allow adding devices that might be discovered later
	}

	err = GM.AddDeviceToSubnet(relmName, regionName, zoneName, subnetName, deviceRequest.DeviceMac)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	response := map[string]string{
		"message":   "device added successfully",
		"deviceMac": deviceRequest.DeviceMac,
		"subnet":    subnetName,
	}

	jsonBytes, err := json.Marshal(response)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleRemoveDeviceFromSubnet removes a device from a subnet
//
// DELETE /api/v1/groups/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/devices/{deviceMac}
//
//	removes the specified device from the subnet
func HandleRemoveDeviceFromSubnet(w http.ResponseWriter, r *http.Request) {
	relmName := chi.URLParam(r, "relmName")
	regionName := chi.URLParam(r, "regionName")
	zoneName := chi.URLParam(r, "zoneName")
	subnetName := chi.URLParam(r, "subnetName")
	deviceMac := chi.URLParam(r, "deviceMac")

	if relmName == "" || regionName == "" || zoneName == "" || subnetName == "" || deviceMac == "" {
		RespondWithError(w, fmt.Errorf("all path parameters are required"))
		return
	}

	err := GM.RemoveDeviceFromSubnet(relmName, regionName, zoneName, subnetName, deviceMac)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	response := map[string]string{
		"message":   "device removed successfully",
		"deviceMac": deviceMac,
		"subnet":    subnetName,
	}

	jsonBytes, err := json.Marshal(response)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleGetUnassignedDevices returns devices not assigned to any subnet
//
// GET /api/v1/groups/devices/unassigned
//
//	returns array of unassigned devices
func HandleGetUnassignedDevices(w http.ResponseWriter, r *http.Request) {
	devices := GM.GetUnassignedDevices()

	jsonBytes, err := json.Marshal(devices)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleGetDevicesForTransfer returns devices formatted for Transfer component
//
// GET /api/v1/groups/devices/transfer
//
//	returns array of devices with transfer format
func HandleGetDevicesForTransfer(w http.ResponseWriter, r *http.Request) {
	devices := GM.GetDevicesForTransfer()

	jsonBytes, err := json.Marshal(devices)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleGetAvailableDevicesForSubnet returns available devices for a subnet
//
// GET /api/v1/groups/devices/available/{subnetName}
//
//	returns array of available devices for the subnet
func HandleGetAvailableDevicesForSubnet(w http.ResponseWriter, r *http.Request) {
	subnetName := chi.URLParam(r, "subnetName")
	if subnetName == "" {
		RespondWithError(w, fmt.Errorf("subnet name is required"))
		return
	}

	devices := GM.GetAvailableDevicesForSubnet(subnetName)

	jsonBytes, err := json.Marshal(devices)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleGetGroupStatistics returns group and device statistics
//
// GET /api/v1/groups/statistics
//
//	returns statistics about groups and devices
func HandleGetGroupStatistics(w http.ResponseWriter, r *http.Request) {
	stats := GM.GetGroupStatistics()

	jsonBytes, err := json.Marshal(stats)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// SubnetGroupInfo represents subnet group information with hierarchical context
type SubnetGroupInfo struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Comment     string   `json:"comment,omitempty"`
	RelmName    string   `json:"relmName"`
	RegionName  string   `json:"regionName"`
	ZoneName    string   `json:"zoneName"`
	SubnetName  string   `json:"subnetName"`
	DeviceCount int      `json:"deviceCount"`
	Devices     []string `json:"devices"`
	CreatedAt   string   `json:"created_at"`
	UpdatedAt   string   `json:"updated_at"`
}

// HandleGetSubnetGroups returns all subnet groups with their hierarchical information
//
// GET /api/v1/subnet-groups
//
//	returns array of subnet groups with relm, region, zone, subnet context and device counts, including "All Devices" entry
func HandleGetSubnetGroups(w http.ResponseWriter, r *http.Request) {
	var subnetGroups []SubnetGroupInfo

	QC.GroupMutex.Lock()
	QC.DevMutex.Lock()
	defer QC.GroupMutex.Unlock()
	defer QC.DevMutex.Unlock()

	// Add "All Devices" entry first
	allDevicesInfo := SubnetGroupInfo{
		ID:          "all",
		Name:        "all-devices",
		Comment:     "Show all devices",
		RelmName:    "all-relms",
		RegionName:  "",
		ZoneName:    "",
		SubnetName:  "",
		DeviceCount: len(QC.DevData) - 1, // -1 to exclude the dummy entry
		Devices:     []string{},
		CreatedAt:   "",
		UpdatedAt:   "",
	}
	subnetGroups = append(subnetGroups, allDevicesInfo)

	// Iterate through all relms, regions, zones, subnets, and subnet groups
	for relmName, relm := range QC.GroupData {
		for _, region := range relm.Regions {
			for _, zone := range region.Zones {
				for _, subnet := range zone.Subnets {
					for _, subnetGroup := range subnet.SubnetGroups {
						// Create unique ID for the subnet group
						id := fmt.Sprintf("%s-%s-%s-%s-%s", relmName, region.Name, zone.Name, subnet.Name, subnetGroup.Name)

						subnetGroupInfo := SubnetGroupInfo{
							ID:          id,
							Name:        subnetGroup.Name,
							Comment:     subnetGroup.Comment,
							RelmName:    relmName,
							RegionName:  region.Name,
							ZoneName:    zone.Name,
							SubnetName:  subnet.Name,
							DeviceCount: len(subnetGroup.Devices),
							Devices:     subnetGroup.Devices,
							CreatedAt:   subnetGroup.CreatedAt,
							UpdatedAt:   subnetGroup.UpdatedAt,
						}

						subnetGroups = append(subnetGroups, subnetGroupInfo)
					}
				}
			}
		}
	}

	jsonBytes, err := json.Marshal(subnetGroups)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}
